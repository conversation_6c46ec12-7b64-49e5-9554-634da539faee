#!/usr/bin/env python3
"""
Multi-Camera Fusion System for Amino Grape Product Tracking
Tracks products across all 4 cameras simultaneously with data fusion
"""

import cv2
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
from onnx_product_hand_tracking_system import ONNXProductHandTracker
import threading
import queue
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiCameraFusionSystem:
    def __init__(self):
        """Initialize multi-camera fusion system"""
        self.trackers = {}  # One tracker per camera
        self.camera_data = {}  # Store detection data from each camera
        self.fusion_results = {}  # Fused tracking results
        self.frame_sync_tolerance = 2  # Frames tolerance for synchronization
        
        # Initialize trackers for each camera
        for cam_id in range(4):
            self.trackers[cam_id] = ONNXProductHandTracker()
            self.camera_data[cam_id] = {
                'detections': [],
                'frame_count': 0,
                'timestamp': 0
            }
    
    def calculate_3d_position(self, detections: Dict[int, Dict]) -> Optional[Tuple[float, float, float]]:
        """
        Estimate 3D position of product using multi-camera triangulation
        This is a simplified version - in production would use camera calibration
        """
        if len(detections) < 2:
            return None
        
        # Simple geometric estimation based on camera positions
        # In production, this would use proper camera calibration matrices
        positions = []
        for cam_id, detection in detections.items():
            bbox = detection['bbox']
            center_x = (bbox[0] + bbox[2]) / 2
            center_y = (bbox[1] + bbox[3]) / 2
            
            # Estimate 3D position based on camera angle (simplified)
            if cam_id == 0:  # Front camera
                x, y, z = center_x - 640, center_y - 360, 1000
            elif cam_id == 1:  # Right camera  
                x, y, z = 640, center_y - 360, center_x - 640
            elif cam_id == 2:  # Back camera
                x, y, z = 640 - center_x, center_y - 360, -1000
            elif cam_id == 3:  # Left camera
                x, y, z = -640, center_y - 360, 640 - center_x
            
            positions.append((x, y, z))
        
        # Average the positions (simple fusion)
        if positions:
            avg_x = sum(p[0] for p in positions) / len(positions)
            avg_y = sum(p[1] for p in positions) / len(positions)
            avg_z = sum(p[2] for p in positions) / len(positions)
            return (avg_x, avg_y, avg_z)
        
        return None
    
    def fuse_detections(self, frame_number: int) -> Dict:
        """
        Fuse detections from all cameras for a given frame
        """
        frame_detections = {}
        confidences = {}
        
        # Collect detections from all cameras for this frame
        for cam_id in range(4):
            cam_data = self.camera_data[cam_id]
            if (frame_number < len(cam_data['detections']) and 
                cam_data['detections'][frame_number]):
                
                detection = cam_data['detections'][frame_number][0]  # Get first detection
                frame_detections[cam_id] = detection
                confidences[cam_id] = detection['confidence']
        
        if not frame_detections:
            return {}
        
        # Calculate 3D position
        position_3d = self.calculate_3d_position(frame_detections)
        
        # Determine best camera view (highest confidence)
        best_cam = max(confidences.keys(), key=lambda k: confidences[k])
        
        # Create fused result
        fused_result = {
            'frame': frame_number,
            'primary_camera': best_cam,
            'detections_count': len(frame_detections),
            'cameras_detecting': list(frame_detections.keys()),
            'average_confidence': sum(confidences.values()) / len(confidences),
            'max_confidence': max(confidences.values()),
            'position_3d': position_3d,
            'primary_detection': frame_detections[best_cam],
            'all_detections': frame_detections
        }
        
        return fused_result
    
    def process_camera_video(self, cam_id: int, video_path: str) -> List[List[Dict]]:
        """Process a single camera video and return detections per frame"""
        logger.info(f"🎥 Processing camera {cam_id}: {video_path}")
        
        tracker = self.trackers[cam_id]
        cap = cv2.VideoCapture(video_path)
        
        frame_detections = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Get detections for this frame
            detections = tracker.detect_frame(frame)
            frame_detections.append(detections)
            
            frame_count += 1
            if frame_count % 50 == 0:
                logger.info(f"  Camera {cam_id}: Processed {frame_count} frames")
        
        cap.release()
        
        logger.info(f"✅ Camera {cam_id}: Completed {frame_count} frames")
        return frame_detections
    
    def process_multi_camera_videos(self, video_paths: Dict[int, str]) -> Dict:
        """
        Process all camera videos simultaneously and fuse results
        """
        logger.info("🚀 Starting Multi-Camera Fusion Processing")
        logger.info("=" * 60)
        
        # Process each camera video
        for cam_id, video_path in video_paths.items():
            detections = self.process_camera_video(cam_id, video_path)
            self.camera_data[cam_id]['detections'] = detections
            self.camera_data[cam_id]['frame_count'] = len(detections)
        
        # Determine the minimum frame count across all cameras
        min_frames = min(self.camera_data[cam_id]['frame_count'] 
                        for cam_id in video_paths.keys())
        
        logger.info(f"📊 Synchronizing {min_frames} frames across {len(video_paths)} cameras")
        
        # Fuse detections frame by frame
        fusion_results = []
        total_detections = 0
        multi_camera_frames = 0
        
        for frame_num in range(min_frames):
            fused_frame = self.fuse_detections(frame_num)
            
            if fused_frame:
                fusion_results.append(fused_frame)
                total_detections += fused_frame['detections_count']
                
                if fused_frame['detections_count'] > 1:
                    multi_camera_frames += 1
            
            if frame_num % 100 == 0:
                logger.info(f"  Fused frame {frame_num}/{min_frames}")
        
        # Generate summary statistics
        summary = {
            'total_frames': min_frames,
            'frames_with_detections': len(fusion_results),
            'total_detections': total_detections,
            'multi_camera_frames': multi_camera_frames,
            'detection_rate': len(fusion_results) / min_frames * 100,
            'multi_camera_rate': multi_camera_frames / len(fusion_results) * 100 if fusion_results else 0,
            'average_confidence': sum(f['average_confidence'] for f in fusion_results) / len(fusion_results) if fusion_results else 0,
            'camera_statistics': {}
        }
        
        # Per-camera statistics
        for cam_id in video_paths.keys():
            cam_detections = sum(1 for f in fusion_results if cam_id in f['cameras_detecting'])
            summary['camera_statistics'][cam_id] = {
                'frames_with_detections': cam_detections,
                'detection_rate': cam_detections / min_frames * 100
            }
        
        return {
            'fusion_results': fusion_results,
            'summary': summary,
            'camera_data': self.camera_data
        }
    
    def create_fusion_video(self, video_paths: Dict[int, str], fusion_data: Dict, 
                           output_path: str) -> None:
        """
        Create a video showing all 4 camera feeds with fusion results
        """
        logger.info(f"🎬 Creating fusion video: {output_path}")
        
        # Open all video captures
        caps = {}
        for cam_id, video_path in video_paths.items():
            caps[cam_id] = cv2.VideoCapture(video_path)
        
        # Get video properties
        fps = int(caps[0].get(cv2.CAP_PROP_FPS))
        width = int(caps[0].get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(caps[0].get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Create output video (2x2 grid)
        output_width = width * 2
        output_height = height * 2
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (output_width, output_height))
        
        fusion_results = fusion_data['fusion_results']
        frame_num = 0
        
        while True:
            frames = {}
            all_valid = True
            
            # Read frames from all cameras
            for cam_id in range(4):
                ret, frame = caps[cam_id].read()
                if not ret:
                    all_valid = False
                    break
                frames[cam_id] = frame.copy()
            
            if not all_valid:
                break
            
            # Find fusion result for this frame
            fusion_frame = None
            for result in fusion_results:
                if result['frame'] == frame_num:
                    fusion_frame = result
                    break
            
            # Draw detections and fusion info on each frame
            for cam_id in range(4):
                frame = frames[cam_id]
                
                # Draw camera label
                cv2.putText(frame, f"Camera {cam_id}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                if fusion_frame and cam_id in fusion_frame['cameras_detecting']:
                    detection = fusion_frame['all_detections'][cam_id]
                    bbox = detection['bbox']
                    confidence = detection['confidence']
                    
                    # Draw bounding box
                    color = (0, 255, 0) if cam_id == fusion_frame['primary_camera'] else (0, 255, 255)
                    cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                    
                    # Draw confidence
                    cv2.putText(frame, f"Conf: {confidence:.3f}", 
                               (bbox[0], bbox[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                    
                    # Mark primary camera
                    if cam_id == fusion_frame['primary_camera']:
                        cv2.putText(frame, "PRIMARY", (bbox[0], bbox[3] + 25), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                frames[cam_id] = frame
            
            # Create 2x2 grid
            top_row = np.hstack([frames[0], frames[1]])
            bottom_row = np.hstack([frames[2], frames[3]])
            combined_frame = np.vstack([top_row, bottom_row])
            
            # Add fusion information overlay
            if fusion_frame:
                info_text = [
                    f"Frame: {frame_num}",
                    f"Cameras: {len(fusion_frame['cameras_detecting'])}/4",
                    f"Avg Conf: {fusion_frame['average_confidence']:.3f}",
                    f"Primary: Cam {fusion_frame['primary_camera']}"
                ]
                
                for i, text in enumerate(info_text):
                    cv2.putText(combined_frame, text, (10, output_height - 100 + i * 25),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            
            out.write(combined_frame)
            frame_num += 1
            
            if frame_num % 100 == 0:
                logger.info(f"  Created {frame_num} frames")
        
        # Cleanup
        for cap in caps.values():
            cap.release()
        out.release()
        
        logger.info(f"✅ Fusion video created: {output_path}")

def test_amino_grape_multi_camera_fusion():
    """Test multi-camera fusion on amino grape videos"""
    
    # Define video paths for all 4 cameras
    video_paths = {
        0: "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4",
        1: "QuadCamTestVideos/AminoEnergyGrape/cam1.mp4", 
        2: "QuadCamTestVideos/AminoEnergyGrape/cam2.mp4",
        3: "QuadCamTestVideos/AminoEnergyGrape/cam3.mp4"
    }
    
    # Verify all videos exist
    for cam_id, path in video_paths.items():
        if not Path(path).exists():
            logger.error(f"❌ Video not found: {path}")
            return
    
    logger.info("🎯 AMINO GRAPE MULTI-CAMERA FUSION TEST")
    logger.info("=" * 60)
    
    # Initialize fusion system
    fusion_system = MultiCameraFusionSystem()
    
    # Process all cameras
    fusion_data = fusion_system.process_multi_camera_videos(video_paths)
    
    # Save results
    results_file = "onnx_tracking_results/amino_grape_multi_camera_fusion.json"
    with open(results_file, 'w') as f:
        # Convert numpy types to native Python types for JSON serialization
        json_data = json.loads(json.dumps(fusion_data, default=str))
        json.dump(json_data, f, indent=2)
    
    # Create fusion video
    fusion_video_path = "onnx_tracking_results/amino_grape_multi_camera_fusion.mp4"
    fusion_system.create_fusion_video(video_paths, fusion_data, fusion_video_path)
    
    # Print results
    summary = fusion_data['summary']
    logger.info("\n" + "=" * 60)
    logger.info("🎯 MULTI-CAMERA FUSION RESULTS")
    logger.info("=" * 60)
    logger.info(f"📊 Total frames processed: {summary['total_frames']}")
    logger.info(f"📊 Frames with detections: {summary['frames_with_detections']}")
    logger.info(f"📊 Detection rate: {summary['detection_rate']:.1f}%")
    logger.info(f"📊 Multi-camera frames: {summary['multi_camera_frames']}")
    logger.info(f"📊 Multi-camera rate: {summary['multi_camera_rate']:.1f}%")
    logger.info(f"📊 Average confidence: {summary['average_confidence']:.3f}")
    
    logger.info(f"\n📹 Per-Camera Performance:")
    for cam_id, stats in summary['camera_statistics'].items():
        logger.info(f"  Camera {cam_id}: {stats['frames_with_detections']} detections ({stats['detection_rate']:.1f}%)")
    
    logger.info(f"\n💾 Results saved to: {results_file}")
    logger.info(f"🎬 Fusion video saved to: {fusion_video_path}")
    
    return fusion_data

if __name__ == "__main__":
    test_amino_grape_multi_camera_fusion()
