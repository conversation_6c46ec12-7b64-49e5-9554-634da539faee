#!/usr/bin/env python3
"""
Custom YOLO + ByteTrack System for Amino Energy Grape Detection
Trains a YOLO model on amino energy grape frames and tests on QuadCamTestVideos with ByteTrack integration
"""

import os
import json
import cv2
import numpy as np
import shutil
from pathlib import Path
import yaml
from ultralytics import <PERSON><PERSON><PERSON>
import logging
from typing import Dict, List, Tuple, Optional
import argparse
from datetime import datetime

# Simple tracking implementation (will add ByteTrack later)
BYTETRACK_AVAILABLE = False

class SimpleTracker:
    """Simple centroid-based tracker as fallback"""
    def __init__(self, max_disappeared=30, max_distance=50):
        self.next_id = 0
        self.objects = {}
        self.disappeared = {}
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance

    def register(self, centroid):
        self.objects[self.next_id] = centroid
        self.disappeared[self.next_id] = 0
        self.next_id += 1

    def deregister(self, object_id):
        del self.objects[object_id]
        del self.disappeared[object_id]

    def update(self, rects):
        if len(rects) == 0:
            for object_id in list(self.disappeared.keys()):
                self.disappeared[object_id] += 1
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister(object_id)
            return []

        input_centroids = np.zeros((len(rects), 2), dtype="int")
        for (i, (x1, y1, x2, y2, _)) in enumerate(rects):
            cx = int((x1 + x2) / 2.0)
            cy = int((y1 + y2) / 2.0)
            input_centroids[i] = (cx, cy)

        if len(self.objects) == 0:
            for i in range(len(input_centroids)):
                self.register(input_centroids[i])
        else:
            object_centroids = list(self.objects.values())
            object_ids = list(self.objects.keys())

            D = np.linalg.norm(np.array(object_centroids)[:, np.newaxis] - input_centroids, axis=2)

            rows = D.min(axis=1).argsort()
            cols = D.argmin(axis=1)[rows]

            used_row_indices = set()
            used_col_indices = set()

            for (row, col) in zip(rows, cols):
                if row in used_row_indices or col in used_col_indices:
                    continue

                if D[row, col] > self.max_distance:
                    continue

                object_id = object_ids[row]
                self.objects[object_id] = input_centroids[col]
                self.disappeared[object_id] = 0

                used_row_indices.add(row)
                used_col_indices.add(col)

            unused_row_indices = set(range(0, D.shape[0])).difference(used_row_indices)
            unused_col_indices = set(range(0, D.shape[1])).difference(used_col_indices)

            if D.shape[0] >= D.shape[1]:
                for row in unused_row_indices:
                    object_id = object_ids[row]
                    self.disappeared[object_id] += 1
                    if self.disappeared[object_id] > self.max_disappeared:
                        self.deregister(object_id)
            else:
                for col in unused_col_indices:
                    self.register(input_centroids[col])

        # Return tracking results in format similar to ByteTrack
        results = []
        for object_id, centroid in self.objects.items():
            # Create a simple track object
            class SimpleTrack:
                def __init__(self, track_id, centroid, score=0.8):
                    self.track_id = track_id
                    self.score = score
                    # Estimate bounding box from centroid (simplified)
                    self.tlwh = [centroid[0]-25, centroid[1]-25, 50, 50]

            results.append(SimpleTrack(object_id, centroid))

        return results

class AminoYOLOByteTrackSystem:
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.images_dir = self.base_dir / "Images" / "Amino_Energy_Grape-segmentation-labelme-extracted"
        self.quad_videos_dir = self.base_dir / "QuadCamTestVideos" / "AminoEnergyGrape"
        self.dataset_dir = self.base_dir / "custom_amino_bytetrack_dataset"
        self.models_dir = self.base_dir / "custom_amino_bytetrack_models"
        self.results_dir = self.base_dir / "custom_amino_bytetrack_results"
        
        # Create directories
        for dir_path in [self.dataset_dir, self.models_dir, self.results_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('custom_amino_yolo_bytetrack.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # ByteTracker configuration
        self.tracker_config = {
            'track_thresh': 0.5,
            'track_buffer': 30,
            'match_thresh': 0.8,
            'frame_rate': 30
        }
        
    def convert_labelme_to_yolo(self) -> Dict:
        """Convert LabelMe annotations to YOLO format"""
        self.logger.info("Converting LabelMe annotations to YOLO format...")
        
        train_dir = self.dataset_dir / "train"
        val_dir = self.dataset_dir / "val"
        
        for split_dir in [train_dir, val_dir]:
            (split_dir / "images").mkdir(parents=True, exist_ok=True)
            (split_dir / "labels").mkdir(parents=True, exist_ok=True)
        
        # Get all annotation files
        json_files = list(self.images_dir.glob("*.json"))
        self.logger.info(f"Found {len(json_files)} annotation files")
        
        # Split data: 80% train, 20% val
        split_idx = int(len(json_files) * 0.8)
        train_files = json_files[:split_idx]
        val_files = json_files[split_idx:]
        
        stats = {"train": 0, "val": 0, "total_annotations": 0}
        
        for split_name, files in [("train", train_files), ("val", val_files)]:
            split_dir = train_dir if split_name == "train" else val_dir
            
            for json_file in files:
                # Load annotation
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                image_path = self.images_dir / data['imagePath']
                if not image_path.exists():
                    continue
                
                # Copy image
                dest_image = split_dir / "images" / image_path.name
                shutil.copy2(image_path, dest_image)
                
                # Convert annotations
                img_width = data['imageWidth']
                img_height = data['imageHeight']
                
                yolo_annotations = []
                for shape in data['shapes']:
                    if shape['shape_type'] == 'polygon' and shape['label'] == 'Amino_Energy_Grape':
                        # Convert polygon to bounding box
                        points = np.array(shape['points'])
                        x_min, y_min = points.min(axis=0)
                        x_max, y_max = points.max(axis=0)
                        
                        # Convert to YOLO format (normalized)
                        x_center = (x_min + x_max) / 2 / img_width
                        y_center = (y_min + y_max) / 2 / img_height
                        width = (x_max - x_min) / img_width
                        height = (y_max - y_min) / img_height
                        
                        # Class 0 for Amino_Energy_Grape
                        yolo_annotations.append(f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
                        stats["total_annotations"] += 1
                
                # Save YOLO annotation file
                if yolo_annotations:
                    label_file = split_dir / "labels" / f"{image_path.stem}.txt"
                    with open(label_file, 'w') as f:
                        f.write('\n'.join(yolo_annotations))
                    
                    stats[split_name] += 1
        
        # Create dataset.yaml
        dataset_yaml = {
            'path': str(self.dataset_dir.absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'nc': 1,
            'names': ['Amino_Energy_Grape']
        }
        
        with open(self.dataset_dir / "dataset.yaml", 'w') as f:
            yaml.dump(dataset_yaml, f)
        
        self.logger.info(f"Dataset conversion complete: {stats}")
        return stats
    
    def train_yolo_model(self, model_size: str = "n", epochs: int = 100) -> str:
        """Train YOLO model"""
        self.logger.info(f"Training YOLO{model_size} model for {epochs} epochs...")
        
        # Initialize model
        model = YOLO(f'yolov8{model_size}.pt')
        
        # Train
        results = model.train(
            data=str(self.dataset_dir / "dataset.yaml"),
            epochs=epochs,
            imgsz=640,
            batch=16,
            device=0,  # Use GPU if available
            project=str(self.models_dir),
            name=f"amino_grape_yolo{model_size}",
            save=True,
            plots=True,
            val=True,
            patience=20,
            save_period=10
        )
        
        # Get best model path
        best_model_path = self.models_dir / f"amino_grape_yolo{model_size}" / "weights" / "best.pt"
        
        self.logger.info(f"Training complete. Best model: {best_model_path}")
        return str(best_model_path)
    
    def setup_tracker(self) -> SimpleTracker:
        """Setup Simple Tracker (fallback for ByteTrack)"""
        self.logger.info("Setting up simple centroid-based tracker")
        return SimpleTracker(max_disappeared=30, max_distance=100)

    def detect_and_track_video(self, model_path: str, video_path: str, output_path: str) -> Dict:
        """Detect and track objects in video using YOLO + Movement-based Single Object Tracking"""
        self.logger.info(f"Processing video: {video_path}")

        # Load model
        model = YOLO(model_path)

        # Open video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")

        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Setup video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

        # Detection and tracking statistics
        stats = {
            "total_frames": total_frames,
            "detections": 0,
            "tracks": 0,
            "confidence_scores": [],
            "track_lengths": {}
        }

        # Movement detection setup
        prev_frame = None
        current_track = None
        track_id = 0
        frames_without_detection = 0
        max_frames_without_detection = 10

        frame_idx = 0

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Convert to grayscale for movement detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # YOLO detection with higher confidence threshold
            results = model(frame, conf=0.6, iou=0.4)

            # Extract and filter detections
            valid_detections = []
            if len(results[0].boxes) > 0:
                boxes = results[0].boxes.xyxy.cpu().numpy()
                scores = results[0].boxes.conf.cpu().numpy()
                classes = results[0].boxes.cls.cpu().numpy()

                # Filter for amino grape class and high confidence
                for box, score, cls in zip(boxes, scores, classes):
                    if int(cls) == 0 and score > 0.6:  # Higher confidence threshold
                        x1, y1, x2, y2 = box

                        # Check if detection has reasonable size (not too small/large)
                        box_width = x2 - x1
                        box_height = y2 - y1
                        box_area = box_width * box_height
                        frame_area = width * height

                        # Filter out boxes that are too small or too large
                        if 0.0005 < (box_area / frame_area) < 0.5:
                            valid_detections.append([x1, y1, x2, y2, score])

            # Movement-based filtering and single object tracking
            best_detection = None
            if valid_detections and prev_frame is not None:
                # Calculate movement for each detection
                detection_scores = []

                for detection in valid_detections:
                    x1, y1, x2, y2, conf = detection

                    # Extract region of interest
                    roi_current = gray[int(y1):int(y2), int(x1):int(x2)]
                    roi_prev = prev_frame[int(y1):int(y2), int(x1):int(x2)]

                    if roi_current.size > 0 and roi_prev.size > 0 and roi_current.shape == roi_prev.shape:
                        # Calculate movement score using frame difference
                        diff = cv2.absdiff(roi_current, roi_prev)
                        movement_score = np.mean(diff)

                        # Combined score: confidence + movement
                        combined_score = conf * 0.7 + (movement_score / 255.0) * 0.3
                        detection_scores.append((detection, combined_score, movement_score))

                # Select detection with highest combined score (confidence + movement)
                if detection_scores:
                    # Sort by combined score and take the best one
                    detection_scores.sort(key=lambda x: x[1], reverse=True)
                    best_detection, best_score, movement = detection_scores[0]

                    # Accept if there's movement OR high confidence (product in hand)
                    if movement > 2.0 or best_detection[4] > 0.8:  # Lower movement threshold or high confidence
                        stats["detections"] += 1
                        stats["confidence_scores"].append(float(best_detection[4]))
                        frames_without_detection = 0
                    else:
                        best_detection = None
                        frames_without_detection += 1
                else:
                    frames_without_detection += 1
            elif valid_detections:
                # First frame or no previous frame - take highest confidence detection
                best_detection = max(valid_detections, key=lambda x: x[4])
                stats["detections"] += 1
                stats["confidence_scores"].append(float(best_detection[4]))
                frames_without_detection = 0
            else:
                frames_without_detection += 1

            # Single object tracking logic
            if best_detection is not None:
                x1, y1, x2, y2, conf = best_detection

                # Update or create track
                if current_track is None:
                    current_track = {
                        'id': track_id,
                        'bbox': [x1, y1, x2, y2],
                        'confidence': conf,
                        'frames': 1
                    }
                    stats["tracks"] = 1
                    stats["track_lengths"][track_id] = 1
                else:
                    # Update existing track
                    current_track['bbox'] = [x1, y1, x2, y2]
                    current_track['confidence'] = conf
                    current_track['frames'] += 1
                    stats["track_lengths"][track_id] += 1

                # Draw single bounding box with tracking info
                cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 3)

                # Draw confidence and track info
                label = f"ID:{track_id} Amino Grape: {conf:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]

                # Background for text
                cv2.rectangle(frame, (int(x1), int(y1) - label_size[1] - 15),
                            (int(x1) + label_size[0] + 10, int(y1)), (0, 255, 0), -1)

                # Text
                cv2.putText(frame, label, (int(x1) + 5, int(y1) - 8),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

                # Draw center point
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                cv2.circle(frame, (center_x, center_y), 5, (0, 0, 255), -1)

            elif current_track is not None and frames_without_detection > max_frames_without_detection:
                # Lost track for too long, reset
                current_track = None
                track_id += 1

            # Add frame info and detection status
            info_text = f"Frame: {frame_idx}/{total_frames}"
            cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            status_text = f"Tracking: {'Active' if current_track else 'Searching'}"
            cv2.putText(frame, status_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

            # Store current frame for next iteration
            prev_frame = gray.copy()

            # Write frame
            out.write(frame)

            frame_idx += 1
            if frame_idx % 100 == 0:
                self.logger.info(f"Processed {frame_idx}/{total_frames} frames - Detections: {stats['detections']}")

        # Cleanup
        cap.release()
        out.release()

        # Calculate final statistics
        if stats["confidence_scores"]:
            stats["avg_confidence"] = float(np.mean(stats["confidence_scores"]))
            stats["min_confidence"] = float(np.min(stats["confidence_scores"]))
            stats["max_confidence"] = float(np.max(stats["confidence_scores"]))
        else:
            stats["avg_confidence"] = 0.0
            stats["min_confidence"] = 0.0
            stats["max_confidence"] = 0.0

        self.logger.info(f"Video processing complete: {stats}")
        return stats

    def process_all_quad_videos(self, model_path: str) -> Dict:
        """Process all QuadCam test videos"""
        self.logger.info("Processing all QuadCam test videos...")

        all_results = {}
        video_files = list(self.quad_videos_dir.glob("*.mp4"))

        for video_file in video_files:
            camera_name = video_file.stem  # cam0, cam1, cam2, cam3
            output_file = self.results_dir / f"{camera_name}_amino_grape_bytetrack.mp4"

            try:
                stats = self.detect_and_track_video(model_path, video_file, output_file)
                all_results[camera_name] = {
                    "video_path": str(video_file),
                    "output_path": str(output_file),
                    "stats": stats,
                    "status": "success"
                }
            except Exception as e:
                self.logger.error(f"Error processing {video_file}: {e}")
                all_results[camera_name] = {
                    "video_path": str(video_file),
                    "error": str(e),
                    "status": "failed"
                }

        # Save results summary
        results_file = self.results_dir / "bytetrack_results_summary.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2)

        self.logger.info(f"All videos processed. Results saved to {results_file}")
        return all_results

    def run_complete_pipeline(self, model_size: str = "n", epochs: int = 100) -> Dict:
        """Run the complete pipeline: data preparation, training, and testing"""
        self.logger.info("Starting complete YOLO + ByteTrack pipeline...")

        pipeline_results = {
            "start_time": datetime.now().isoformat(),
            "model_size": model_size,
            "epochs": epochs
        }

        try:
            # Step 1: Convert data
            self.logger.info("Step 1: Converting LabelMe annotations to YOLO format...")
            conversion_stats = self.convert_labelme_to_yolo()
            pipeline_results["data_conversion"] = conversion_stats

            # Step 2: Train model
            self.logger.info("Step 2: Training YOLO model...")
            model_path = self.train_yolo_model(model_size, epochs)
            pipeline_results["model_path"] = model_path
            pipeline_results["training_status"] = "completed"

            # Step 3: Test on videos
            self.logger.info("Step 3: Testing on QuadCam videos with ByteTrack...")
            video_results = self.process_all_quad_videos(model_path)
            pipeline_results["video_results"] = video_results

            pipeline_results["status"] = "success"
            pipeline_results["end_time"] = datetime.now().isoformat()

        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            pipeline_results["status"] = "failed"
            pipeline_results["error"] = str(e)
            pipeline_results["end_time"] = datetime.now().isoformat()

        # Save pipeline results
        pipeline_file = self.results_dir / "complete_pipeline_results.json"
        with open(pipeline_file, 'w') as f:
            json.dump(pipeline_results, f, indent=2)

        self.logger.info(f"Pipeline complete. Results: {pipeline_file}")
        return pipeline_results

def main():
    parser = argparse.ArgumentParser(description="Custom YOLO + ByteTrack System for Amino Energy Grape")
    parser.add_argument("--model-size", choices=["n", "s", "m", "l", "x"], default="n",
                       help="YOLO model size (default: n)")
    parser.add_argument("--epochs", type=int, default=100,
                       help="Training epochs (default: 100)")
    parser.add_argument("--data-only", action="store_true",
                       help="Only convert data, don't train")
    parser.add_argument("--test-only", type=str,
                       help="Only test with existing model (provide model path)")

    args = parser.parse_args()

    # Initialize system
    system = AminoYOLOByteTrackSystem()

    if args.data_only:
        # Only convert data
        system.convert_labelme_to_yolo()
    elif args.test_only:
        # Only test with existing model
        system.process_all_quad_videos(args.test_only)
    else:
        # Run complete pipeline
        system.run_complete_pipeline(args.model_size, args.epochs)

if __name__ == "__main__":
    main()
