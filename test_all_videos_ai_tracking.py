#!/usr/bin/env python3
"""
Test AI motion tracking on all available videos
"""

import os
import json
from pathlib import Path
from onnx_product_hand_tracking_system import ONNXProductHandTracker
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_all_videos():
    """Test AI motion tracking on all available videos"""
    
    tracker = ONNXProductHandTracker()
    
    # Define video directories to test
    video_directories = [
        "QuadCamTestVideos/AminoEnergyGrape",
        "new_test_videos",
        "processed_videos"
    ]
    
    all_results = {}
    total_videos_processed = 0
    
    logger.info("🚀 Starting comprehensive AI motion tracking test on all videos")
    logger.info("=" * 70)
    
    for video_dir in video_directories:
        if not Path(video_dir).exists():
            logger.warning(f"Directory not found: {video_dir}")
            continue
            
        logger.info(f"\n📁 Processing directory: {video_dir}")
        logger.info("-" * 50)
        
        dir_results = {}
        
        if video_dir == "QuadCamTestVideos/AminoEnergyGrape":
            # Test QuadCam videos
            for cam_num in range(4):
                video_path = Path(video_dir) / f"cam{cam_num}.mp4"
                if video_path.exists():
                    logger.info(f"📹 Processing {video_path}")
                    
                    output_path = f"onnx_tracking_results/ai_motion_cam{cam_num}.mp4"
                    result = tracker.process_video(str(video_path), output_path)
                    
                    dir_results[f"cam{cam_num}"] = result
                    total_videos_processed += 1
                    
                    logger.info(f"   ✅ Detections: {result['total_detections']}, Tracks: {result['unique_tracks']}, Conf: {result['avg_confidence']:.3f}")
                else:
                    logger.warning(f"   ❌ Video not found: {video_path}")
        
        else:
            # Test other video directories
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
            video_files = []
            
            for ext in video_extensions:
                video_files.extend(Path(video_dir).glob(f"*{ext}"))
            
            for video_path in sorted(video_files)[:10]:  # Limit to first 10 videos per directory
                logger.info(f"📹 Processing {video_path}")
                
                video_name = video_path.stem
                output_path = f"onnx_tracking_results/ai_motion_{video_name}.mp4"
                
                try:
                    result = tracker.process_video(str(video_path), output_path)
                    dir_results[video_name] = result
                    total_videos_processed += 1
                    
                    logger.info(f"   ✅ Detections: {result['total_detections']}, Tracks: {result['unique_tracks']}, Conf: {result['avg_confidence']:.3f}")
                    
                except Exception as e:
                    logger.error(f"   ❌ Failed to process {video_path}: {e}")
                    dir_results[video_name] = {"status": "error", "error": str(e)}
        
        all_results[video_dir] = dir_results
    
    # Save comprehensive results
    results_file = "onnx_tracking_results/all_videos_ai_motion_results.json"
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # Generate summary
    logger.info("\n" + "=" * 70)
    logger.info("📊 COMPREHENSIVE AI MOTION TRACKING SUMMARY")
    logger.info("=" * 70)
    
    total_detections = 0
    total_tracks = 0
    confidence_scores = []
    
    for dir_name, dir_results in all_results.items():
        logger.info(f"\n📁 {dir_name}:")
        
        dir_detections = 0
        dir_tracks = 0
        dir_confidences = []
        
        for video_name, result in dir_results.items():
            if isinstance(result, dict) and "total_detections" in result:
                dir_detections += result['total_detections']
                dir_tracks += result['unique_tracks']
                dir_confidences.append(result['avg_confidence'])
                
                logger.info(f"   {video_name}: {result['total_detections']} detections, {result['unique_tracks']} tracks")
        
        if dir_confidences:
            avg_conf = sum(dir_confidences) / len(dir_confidences)
            logger.info(f"   📊 Directory totals: {dir_detections} detections, {dir_tracks} tracks, {avg_conf:.3f} avg conf")
            
            total_detections += dir_detections
            total_tracks += dir_tracks
            confidence_scores.extend(dir_confidences)
    
    # Overall summary
    logger.info(f"\n🎯 OVERALL RESULTS:")
    logger.info(f"   Videos processed: {total_videos_processed}")
    logger.info(f"   Total detections: {total_detections}")
    logger.info(f"   Total tracks: {total_tracks}")
    if confidence_scores:
        logger.info(f"   Average confidence: {sum(confidence_scores)/len(confidence_scores):.3f}")
    logger.info(f"   Results saved to: {results_file}")
    
    return all_results

if __name__ == "__main__":
    test_all_videos()
