#!/usr/bin/env python3
"""
Test aggressive filtering to prevent teleportation to faces and full frame
"""

from improved_hand_product_tracker import ImprovedHandProductTracker
import json
import time
import cv2
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_aggressive_filtering():
    """Test the aggressive filtering system"""
    
    print("🔧 TESTING AGGRESSIVE FILTERING SYSTEM")
    print("=" * 70)
    print("🎯 Testing fixes for:")
    print("   1. Teleportation to faces")
    print("   2. Teleportation to full frame outline")
    print("   3. Edge detections")
    print("   4. Invalid detection patterns")
    print()
    
    # Test video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    
    # Create the filtered video
    print(f"🎬 Creating aggressively filtered tracking video...")
    
    tracker = ImprovedHandProductTracker(
        hand_model_path="yolow-l_product_and_hand_detector.onnx",
        yolo_model_path="yolow-l_product_and_hand_detector.onnx"
    )
    
    # Show current aggressive filtering parameters
    print(f"📊 Aggressive filtering parameters:")
    print(f"   Size smoothing factor: {tracker.size_smoothing_factor}")
    print(f"   Max size change per frame: {tracker.max_size_change_per_frame}")
    print(f"   Track timeout: {tracker.track_timeout}")
    print(f"   Motion threshold: {tracker.motion_threshold}")
    print(f"   Confidence threshold: {tracker.confidence_threshold}")
    print()
    
    output_path = "improved_tracking_results/aggressive_filtered_cam0.mp4"
    
    start_time = time.time()
    result = tracker.process_video(video_path, output_path)
    processing_time = time.time() - start_time
    
    print(f"✅ Aggressive filtering completed in {processing_time:.1f}s")
    print(f"   Detections: {result['total_detections']}")
    print(f"   Tracks: {result['unique_tracks']}")
    print(f"   Avg Confidence: {result['avg_confidence']:.3f}")
    print(f"   Output: {output_path}")
    
    # Expected improvements
    print(f"\n🎯 AGGRESSIVE FILTERING IMPROVEMENTS:")
    print("-" * 40)
    print("✅ Face detection filtering (upper 50% + square aspect)")
    print("✅ Full frame detection filtering (>80% size)")
    print("✅ Edge detection filtering (within 5% of edges)")
    print("✅ Size validation (20px min, 60% frame max)")
    print("✅ Position validation (lower 80% of frame)")
    print("✅ Strict movement limits (1.0x diagonal)")
    print("✅ Strict size change limits (0.5x to 2.0x)")
    
    # Save detailed analysis
    detailed_results = {
        'video_path': video_path,
        'output_path': output_path,
        'processing_time': processing_time,
        'result': result,
        'aggressive_filters_implemented': [
            "FACE DETECTION FILTERING:",
            "- Upper 50% frame detection (expanded from 40%)",
            "- Square aspect ratio detection (0.5 to 2.0)",
            "- Face size detection (0.5% to 30% of frame)",
            "",
            "FULL FRAME FILTERING:",
            "- Width/height >80% of frame",
            "- Area >60% of frame",
            "- Oversized detection filtering",
            "",
            "EDGE DETECTION FILTERING:",
            "- Within 5% of frame edges",
            "- Corner and border detection",
            "",
            "SIZE AND POSITION VALIDATION:",
            "- Minimum 20px product size",
            "- Maximum 60% of frame size",
            "- Products in lower 80% of frame",
            "- Aspect ratio 0.2 to 5.0",
            "",
            "MOVEMENT CONSTRAINTS:",
            "- Maximum 1.0x diagonal movement (reduced from 1.5x)",
            "- Size change 0.5x to 2.0x (reduced from 0.3x to 3.0x)",
            "- Aspect ratio change <50% (reduced from 100%)"
        ],
        'expected_results': [
            "Zero teleportation to faces",
            "Zero teleportation to full frame outline",
            "Zero edge detections",
            "Only valid product-in-hand tracking",
            "Smooth, continuous tracking without jumping"
        ]
    }
    
    results_file = "improved_tracking_results/aggressive_filtering_results.json"
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n💾 Detailed analysis saved to: {results_file}")
    
    return detailed_results

if __name__ == "__main__":
    results = test_aggressive_filtering()
    
    print("\n" + "=" * 70)
    print("🎉 AGGRESSIVE FILTERING TESTING COMPLETE")
    print("=" * 70)
    print("🔧 Aggressive filters implemented:")
    print("   ✅ Face detection filtering (upper 50% + square aspect)")
    print("   ✅ Full frame detection filtering (>80% size)")
    print("   ✅ Edge detection filtering (within 5% of edges)")
    print("   ✅ Size validation (20px min, 60% frame max)")
    print("   ✅ Position validation (lower 80% of frame)")
    print("   ✅ Strict movement limits (1.0x diagonal)")
    print("   ✅ Strict size change limits (0.5x to 2.0x)")
    print("\n🎬 Review the output video to see the improvements!")
    print("   - No more teleportation to faces")
    print("   - No more full frame outline detection")
    print("   - Only valid product-in-hand tracking")
    print("   - Smooth, continuous tracking")
