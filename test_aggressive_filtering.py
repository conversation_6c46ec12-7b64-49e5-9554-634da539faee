#!/usr/bin/env python3
"""
Test more aggressive filtering to eliminate face/person detections
"""

import cv2
import numpy as np
import onnxruntime as ort
from pathlib import Path

def test_aggressive_filtering():
    """Test with very aggressive filtering"""
    
    # Load ONNX model
    model_path = "yolow-l_product_and_hand_detector.onnx"
    session = ort.InferenceSession(model_path)
    
    input_name = session.get_inputs()[0].name
    output_names = [output.name for output in session.get_outputs()]
    
    # Load video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    cap = cv2.VideoCapture(video_path)
    
    frame_count = 0
    confidence_threshold = 0.2
    
    print(f"Testing aggressive filtering on {video_path}...")
    
    def aggressive_product_filter(x1, y1, x2, y2, frame_w, frame_h, confidence):
        """Very aggressive filtering to eliminate person/face detections"""
        bbox_w = x2 - x1
        bbox_h = y2 - y1
        bbox_area = bbox_w * bbox_h
        frame_area = frame_w * frame_h
        area_ratio = bbox_area / frame_area
        
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        rel_center_x = center_x / frame_w
        rel_center_y = center_y / frame_h
        aspect_ratio = bbox_w / bbox_h if bbox_h > 0 else 0
        
        # VERY AGGRESSIVE FILTERS:
        
        # 1. Size - only allow very small detections (products in hand should be small)
        if area_ratio > 0.05:  # More than 5% of frame - likely person
            return False, f"too_large_{area_ratio:.4f}"
        
        # 2. Position - only allow lower-right quadrant (where products are typically handled)
        if rel_center_x < 0.3 or rel_center_y < 0.3:  # Upper-left areas
            return False, f"wrong_position_{rel_center_x:.2f}_{rel_center_y:.2f}"
        
        # 3. Aspect ratio - products should be somewhat rectangular, not too square (faces are square)
        if 0.8 < aspect_ratio < 1.2:  # Square-ish detections are likely faces
            return False, f"too_square_{aspect_ratio:.2f}"
        
        # 4. Confidence - require higher confidence for smaller detections
        min_confidence = 0.25 if area_ratio < 0.01 else 0.2
        if confidence < min_confidence:
            return False, f"low_confidence_{confidence:.3f}"
        
        # 5. Size + position combination
        if area_ratio > 0.02 and rel_center_y < 0.6:  # Medium size in upper area
            return False, f"medium_upper_{area_ratio:.4f}_{rel_center_y:.2f}"
        
        # 6. Very small detections are noise
        if area_ratio < 0.001:  # Less than 0.1% of frame
            return False, f"too_small_{area_ratio:.4f}"
        
        return True, "passed"
    
    total_raw = 0
    total_filtered = 0
    
    while frame_count < 50:  # Process first 50 frames
        ret, frame = cap.read()
        if not ret:
            break
        
        # Preprocess frame
        height, width = 640, 640
        resized = cv2.resize(frame, (width, height))
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        normalized = rgb.astype(np.float32) / 255.0
        input_data = np.transpose(normalized, (2, 0, 1))
        input_data = np.expand_dims(input_data, axis=0)
        
        # Run inference
        outputs = session.run(output_names, {input_name: input_data})
        
        # Parse outputs
        num_dets, boxes, scores, labels = outputs[:4]
        
        # Get number of detections
        if len(num_dets.shape) > 0:
            num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
        else:
            num_detections = int(num_dets)
        
        frame_raw = 0
        frame_filtered = 0
        
        # Process detections
        for i in range(num_detections):
            if i < boxes.shape[1]:
                box = boxes[0, i]
                score = scores[0, i]
                label = labels[0, i]
                
                confidence = float(score)
                class_id = int(label)
                
                if confidence > confidence_threshold and class_id >= 0:
                    x1, y1, x2, y2 = box
                    
                    # Scale to original frame size
                    orig_h, orig_w = frame.shape[:2]
                    x1 = int(x1 * orig_w / width)
                    y1 = int(y1 * orig_h / height)
                    x2 = int(x2 * orig_w / width)
                    y2 = int(y2 * orig_h / height)
                    
                    if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                        frame_raw += 1
                        
                        # Apply aggressive filtering
                        passed, reason = aggressive_product_filter(x1, y1, x2, y2, orig_w, orig_h, confidence)
                        
                        if passed:
                            frame_filtered += 1
                        
                        if frame_count < 5:  # Print details for first few frames
                            bbox_w = x2 - x1
                            bbox_h = y2 - y1
                            area_ratio = (bbox_w * bbox_h) / (orig_w * orig_h)
                            center_x = (x1 + x2) / 2
                            center_y = (y1 + y2) / 2
                            rel_center_x = center_x / orig_w
                            rel_center_y = center_y / orig_h
                            
                            print(f"Frame {frame_count}, Det {i}: conf={confidence:.3f}")
                            print(f"  bbox=[{x1},{y1},{x2},{y2}] area_ratio={area_ratio:.4f}")
                            print(f"  center=({rel_center_x:.2f},{rel_center_y:.2f})")
                            print(f"  result: {'PASS' if passed else 'REJECT'} ({reason})")
        
        total_raw += frame_raw
        total_filtered += frame_filtered
        
        if frame_count % 10 == 0:
            print(f"Frame {frame_count}: {frame_raw} raw -> {frame_filtered} filtered")
        
        frame_count += 1
    
    cap.release()
    
    print(f"\nAggressive Filtering Results:")
    print(f"  Total raw detections: {total_raw}")
    print(f"  Total filtered detections: {total_filtered}")
    print(f"  Reduction: {total_raw - total_filtered} detections")
    print(f"  Reduction rate: {((total_raw - total_filtered) / total_raw * 100):.1f}%" if total_raw > 0 else "N/A")

if __name__ == "__main__":
    test_aggressive_filtering()
