#!/usr/bin/env python3
"""
Improved Hand-Product Tracker with Separated Tracking and Classification
- Uses hand-to-product model for robust tracking (prevents box disappearing)
- Uses YOLO model only within bounding box for accurate product classification
"""

import cv2
import numpy as np
import onnxruntime as ort
import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedHandProductTracker:
    """
    Improved tracker that separates hand-product tracking from product classification
    """
    
    def __init__(self, 
                 hand_model_path: str = "yolow-l_product_and_hand_detector.onnx",
                 yolo_model_path: str = None):
        """
        Initialize improved tracker with separate models
        
        Args:
            hand_model_path: Path to hand-to-product detection model (for tracking)
            yolo_model_path: Path to YOLO model for product classification (optional)
        """
        self.hand_model_path = hand_model_path
        self.yolo_model_path = yolo_model_path
        
        # Results directory
        self.results_dir = Path("improved_tracking_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Tracking parameters - IMPROVED FOR CONTINUOUS TRACKING
        self.confidence_threshold = 0.12  # Even lower for more sensitive detection
        self.nms_threshold = 0.4
        self.max_tracking_distance = 200  # Increased for better tracking
        self.track_timeout = 25  # Significantly increased to prevent freezing

        # Size smoothing parameters to prevent jumping - MORE AGGRESSIVE
        self.size_smoothing_factor = 0.85  # Much more aggressive smoothing
        self.max_size_change_per_frame = 0.15  # Maximum size change per frame (15%)
        
        # Current tracking state
        self.active_tracks = {}
        self.next_track_id = 0
        self.frame_count = 0
        
        # Motion detection for filtering stationary objects - IMPROVED SENSITIVITY
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=False,
            varThreshold=16,  # More sensitive to changes
            history=500       # Longer history for better background model
        )
        self.motion_threshold = 200  # Even more sensitive motion detection
        
        # Load models
        self.hand_session = None
        self.yolo_session = None
        self.load_models()
        
    def load_models(self):
        """Load both hand tracking and YOLO classification models"""
        
        # Load hand-to-product tracking model (primary)
        logger.info(f"Loading hand tracking model: {self.hand_model_path}")
        if not os.path.exists(self.hand_model_path):
            raise FileNotFoundError(f"Hand model not found: {self.hand_model_path}")
        
        self.hand_session = ort.InferenceSession(self.hand_model_path)
        self.hand_input_name = self.hand_session.get_inputs()[0].name
        self.hand_output_names = [output.name for output in self.hand_session.get_outputs()]
        self.hand_input_shape = self.hand_session.get_inputs()[0].shape
        
        logger.info("✅ Hand tracking model loaded successfully")
        logger.info(f"   Input shape: {self.hand_input_shape}")
        logger.info(f"   Input name: {self.hand_input_name}")
        logger.info(f"   Output names: {self.hand_output_names}")
        
        # Load YOLO classification model (optional)
        if self.yolo_model_path and os.path.exists(self.yolo_model_path):
            logger.info(f"Loading YOLO classification model: {self.yolo_model_path}")
            self.yolo_session = ort.InferenceSession(self.yolo_model_path)
            self.yolo_input_name = self.yolo_session.get_inputs()[0].name
            self.yolo_output_names = [output.name for output in self.yolo_session.get_outputs()]
            self.yolo_input_shape = self.yolo_session.get_inputs()[0].shape
            
            logger.info("✅ YOLO classification model loaded successfully")
            logger.info(f"   Input shape: {self.yolo_input_shape}")
        else:
            logger.info("ℹ️  No YOLO classification model provided - using hand model only")
    
    def preprocess_frame(self, frame: np.ndarray, target_size: Tuple[int, int] = (640, 640)) -> np.ndarray:
        """Preprocess frame for ONNX model input"""
        height, width = target_size
        resized = cv2.resize(frame, (width, height))
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        normalized = rgb.astype(np.float32) / 255.0
        input_data = np.transpose(normalized, (2, 0, 1))
        input_data = np.expand_dims(input_data, axis=0)
        return input_data
    
    def detect_motion_areas(self, frame: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect areas of motion in the frame using background subtraction"""
        # Apply background subtraction
        fg_mask = self.background_subtractor.apply(frame)
        
        # Morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
        
        # Find contours of moving objects
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_areas = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.motion_threshold:  # Filter out small motion noise
                x, y, w, h = cv2.boundingRect(contour)
                motion_areas.append((x, y, x + w, y + h))
        
        return motion_areas
    
    def calculate_overlap(self, bbox1: Tuple[int, int, int, int], 
                         bbox2: Tuple[int, int, int, int]) -> float:
        """Calculate overlap ratio between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        
        return intersection / area1 if area1 > 0 else 0.0
    
    def is_moving_detection(self, bbox: Tuple[int, int, int, int],
                           motion_areas: List[Tuple[int, int, int, int]]) -> bool:
        """Check if detection overlaps with motion areas - IMPROVED SENSITIVITY"""
        # If no motion areas detected, be more lenient (might be subtle motion)
        if not motion_areas:
            return True  # Allow detection even without strong motion

        for motion_area in motion_areas:
            overlap = self.calculate_overlap(bbox, motion_area)
            if overlap > 0.1:  # Even more sensitive threshold
                return True

        # Additional check: if bbox is reasonably sized and positioned, allow it
        x1, y1, x2, y2 = bbox
        area = (x2 - x1) * (y2 - y1)
        if 1000 < area < 50000:  # Reasonable product size
            return True

        return False
    
    def detect_hands_products(self, frame: np.ndarray) -> List[Dict]:
        """Detect hands and products using the hand-to-product model"""
        # Detect motion areas first
        motion_areas = self.detect_motion_areas(frame)
        
        # Preprocess frame for hand model
        input_data = self.preprocess_frame(frame)
        
        # Run hand model inference
        outputs = self.hand_session.run(self.hand_output_names, {self.hand_input_name: input_data})
        
        # Parse outputs
        num_dets, boxes, scores, labels = outputs[:4]
        
        # Get number of detections
        if len(num_dets.shape) > 0:
            num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
        else:
            num_detections = int(num_dets)
        
        detections = []
        orig_h, orig_w = frame.shape[:2]
        
        # Process detections
        for i in range(min(num_detections, boxes.shape[1] if len(boxes.shape) > 1 else 0)):
            if i < boxes.shape[1]:
                box = boxes[0, i]
                score = scores[0, i]
                label = labels[0, i]
                
                confidence = float(score)
                class_id = int(label)
                
                if confidence > self.confidence_threshold and class_id >= 0:
                    x1, y1, x2, y2 = box
                    
                    # Scale to original frame size
                    x1 = int(x1 * orig_w / 640)
                    y1 = int(y1 * orig_h / 640)
                    x2 = int(x2 * orig_w / 640)
                    y2 = int(y2 * orig_h / 640)
                    
                    # Ensure valid bounding box
                    if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                        bbox = (x1, y1, x2, y2)

                        # AGGRESSIVE FILTERING: Check if detection is invalid
                        is_invalid, invalid_reason = self.is_invalid_detection(
                            [x1, y1, x2, y2], orig_h, orig_w)

                        if not is_invalid:
                            # Filter based on motion (only keep moving objects)
                            if self.is_moving_detection(bbox, motion_areas):
                                detections.append({
                                    'bbox': [x1, y1, x2, y2],
                                    'confidence': confidence,
                                    'class_id': class_id,
                                    'class_name': 'product_in_hand',
                                    'source': 'hand_model',
                                    'filter_status': 'valid'
                                })
                            else:
                                logger.debug(f"Detection filtered: no motion - {invalid_reason}")
                        else:
                            logger.debug(f"Detection filtered: {invalid_reason}")
        
        # Apply NMS and keep only the best detection
        detections = self.apply_nms(detections)
        
        # CRITICAL: Only keep the SINGLE highest confidence detection
        if len(detections) > 1:
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            detections = [detections[0]]
        
        return detections
    
    def classify_product_in_bbox(self, frame: np.ndarray, bbox: List[int]) -> Optional[Dict]:
        """
        Classify product within the given bounding box using YOLO model
        This is the key improvement - YOLO only looks inside the tracked bounding box
        """
        if not self.yolo_session:
            return None

        x1, y1, x2, y2 = bbox

        # Ensure valid bounding box
        if x2 <= x1 or y2 <= y1:
            return None

        # Extract region of interest (ROI) from the bounding box
        roi = frame[y1:y2, x1:x2]

        if roi.size == 0 or roi.shape[0] < 10 or roi.shape[1] < 10:
            return None

        try:
            # Preprocess ROI for YOLO model
            input_data = self.preprocess_frame(roi)

            # Run YOLO inference on ROI only
            outputs = self.yolo_session.run(self.yolo_output_names, {self.yolo_input_name: input_data})

            # Parse YOLO outputs - REAL DYNAMIC CLASSIFICATION
            num_dets, boxes, scores, labels = outputs[:4]

            # Get number of detections
            if len(num_dets.shape) > 0:
                num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
            else:
                num_detections = int(num_dets)

            best_confidence = 0.0
            best_class_id = -1
            best_class_name = "unknown"

            # Find best classification within ROI
            for i in range(min(num_detections, boxes.shape[1] if len(boxes.shape) > 1 else 0)):
                if i < boxes.shape[1]:
                    score = scores[0, i]
                    label = labels[0, i]

                    confidence = float(score)
                    class_id = int(label)

                    if confidence > best_confidence and confidence > 0.2:
                        best_confidence = confidence
                        best_class_id = class_id
                        best_class_name = self.get_class_name(class_id)

            # Return dynamic classification result
            if best_confidence > 0.2:
                return {
                    'product_class': best_class_name,
                    'classification_confidence': best_confidence,  # REAL dynamic confidence
                    'class_id': best_class_id,
                    'method': 'yolo_roi_dynamic',
                    'roi_size': f"{roi.shape[1]}x{roi.shape[0]}"
                }
            else:
                # Low confidence classification
                return {
                    'product_class': 'low_confidence',
                    'classification_confidence': best_confidence,
                    'class_id': -1,
                    'method': 'yolo_roi_dynamic',
                    'roi_size': f"{roi.shape[1]}x{roi.shape[0]}"
                }

        except Exception as e:
            logger.warning(f"Classification error: {e}")
            return {
                'product_class': 'classification_error',
                'classification_confidence': 0.0,
                'error': str(e),
                'method': 'yolo_roi_dynamic'
            }

    def get_class_name(self, class_id: int) -> str:
        """Get class name from class ID"""
        # Map class IDs to names - adjust based on your model
        class_names = {
            0: 'product_in_hand',
            1: 'amino_grape',
            2: 'bottle',
            3: 'can',
            4: 'hand',
            # Add more classes as needed
        }
        return class_names.get(class_id, f'class_{class_id}')
    
    def apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to remove duplicate detections"""
        if len(detections) <= 1:
            return detections
        
        # Convert to format needed for NMS
        boxes = np.array([d['bbox'] for d in detections], dtype=np.float32)
        scores = np.array([d['confidence'] for d in detections], dtype=np.float32)
        
        # Apply NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(), 
                                  self.confidence_threshold, self.nms_threshold)
        
        if len(indices) > 0:
            indices = indices.flatten()
            return [detections[i] for i in indices]
        
        return []

    def calculate_distance(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate distance between two bounding box centers"""
        center1_x = (bbox1[0] + bbox1[2]) / 2
        center1_y = (bbox1[1] + bbox1[3]) / 2
        center2_x = (bbox2[0] + bbox2[2]) / 2
        center2_y = (bbox2[1] + bbox2[3]) / 2

        return np.sqrt((center1_x - center2_x)**2 + (center1_y - center2_y)**2)

    def calculate_bbox_diagonal(self, bbox: List[int]) -> float:
        """Calculate diagonal length of bounding box"""
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]
        return np.sqrt(width**2 + height**2)

    def calculate_bbox_area(self, bbox: List[int]) -> float:
        """Calculate area of bounding box"""
        return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

    def calculate_aspect_ratio(self, bbox: List[int]) -> float:
        """Calculate aspect ratio of bounding box"""
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]
        return width / height if height > 0 else 0

    def is_invalid_detection(self, bbox: List[int], frame_height: int, frame_width: int) -> Tuple[bool, str]:
        """
        AGGRESSIVE filtering to detect invalid detections (faces, full frame, etc.)
        Returns (is_invalid, reason)
        """
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        width = x2 - x1
        height = y2 - y1

        # Relative position and size in frame
        rel_center_x = center_x / frame_width
        rel_center_y = center_y / frame_height
        rel_width = width / frame_width
        rel_height = height / frame_height

        # Calculate characteristics
        aspect_ratio = width / height if height > 0 else 0
        area_ratio = (width * height) / (frame_width * frame_height)

        # FILTER 1: Face detection (MUCH MORE AGGRESSIVE)
        # Faces are typically in upper 50% of frame (expanded from 40%)
        if rel_center_y < 0.5:
            # Square-ish aspect ratio (faces are roughly square)
            if 0.5 < aspect_ratio < 2.0:
                # Reasonable face size
                if 0.005 < area_ratio < 0.3:
                    return True, "face_detection_upper_frame"

        # FILTER 2: Full frame or near-full frame detection
        if rel_width > 0.8 or rel_height > 0.8:
            return True, "full_frame_detection"

        if area_ratio > 0.6:
            return True, "oversized_detection"

        # FILTER 3: Edge detections (likely false positives)
        edge_threshold = 0.05  # 5% from edges
        if (rel_center_x < edge_threshold or rel_center_x > (1 - edge_threshold) or
            rel_center_y < edge_threshold or rel_center_y > (1 - edge_threshold)):
            return True, "edge_detection"

        # FILTER 4: Extreme aspect ratios (not product-like)
        if aspect_ratio < 0.2 or aspect_ratio > 5.0:
            return True, f"extreme_aspect_ratio_{aspect_ratio:.2f}"

        # FILTER 5: Too small or too large for products
        if area_ratio < 0.001 or area_ratio > 0.4:
            return True, f"invalid_size_{area_ratio:.3f}"

        # FILTER 6: Product-in-hand should be in reasonable position
        # Products in hand are typically in center-lower portion of frame
        if rel_center_y < 0.2:  # Too high (likely face or background)
            return True, "too_high_in_frame"

        # FILTER 7: Reasonable product dimensions
        min_product_size = 20  # Minimum 20 pixels
        max_product_size = min(frame_width * 0.6, frame_height * 0.6)  # Max 60% of frame

        if width < min_product_size or height < min_product_size:
            return True, "too_small"

        if width > max_product_size or height > max_product_size:
            return True, "too_large"

        return False, "valid_detection"

    def validate_track_transition(self, prev_bbox: List[int], new_bbox: List[int],
                                 frame_height: int, frame_width: int) -> Tuple[bool, str]:
        """
        AGGRESSIVE validation to prevent teleportation to faces, full frame, etc.
        """
        # FIRST: Check if new detection is invalid (face, full frame, etc.)
        is_invalid, invalid_reason = self.is_invalid_detection(new_bbox, frame_height, frame_width)
        if is_invalid:
            return False, f"invalid_detection_{invalid_reason}"

        # Calculate movement distance
        distance = self.calculate_distance(prev_bbox, new_bbox)
        prev_diagonal = self.calculate_bbox_diagonal(prev_bbox)

        # MUCH MORE STRICT spatial constraint for product tracking
        max_movement = 1.0 * prev_diagonal  # Reduced from 1.5x to 1.0x
        if distance > max_movement:
            return False, f"teleportation_distance_{distance:.1f}_max_{max_movement:.1f}"

        # STRICT size consistency check
        prev_area = self.calculate_bbox_area(prev_bbox)
        new_area = self.calculate_bbox_area(new_bbox)
        size_ratio = new_area / prev_area if prev_area > 0 else 1.0

        # MUCH MORE STRICT size changes (0.5x to 2.0x instead of 0.3x to 3.0x)
        if not (0.5 <= size_ratio <= 2.0):
            return False, f"size_change_{size_ratio:.2f}"

        # STRICT aspect ratio validation
        prev_aspect = self.calculate_aspect_ratio(prev_bbox)
        new_aspect = self.calculate_aspect_ratio(new_bbox)
        aspect_change = abs(new_aspect - prev_aspect) / prev_aspect if prev_aspect > 0 else 0

        # Aspect ratio should not change more than 50% (reduced from 100%)
        if aspect_change > 0.5:
            return False, f"aspect_change_{aspect_change:.2f}"

        # Additional check: ensure new detection is in reasonable product area
        new_center_y = (new_bbox[1] + new_bbox[3]) / 2
        rel_new_center_y = new_center_y / frame_height

        # Products should be in lower 80% of frame (not upper 20%)
        if rel_new_center_y < 0.2:
            return False, "new_detection_too_high"

        return True, "valid_transition"

    def smooth_bbox_size(self, prev_bbox: List[int], new_bbox: List[int]) -> List[int]:
        """
        AGGRESSIVE smooth bounding box size changes to prevent jumping
        """
        prev_width = prev_bbox[2] - prev_bbox[0]
        prev_height = prev_bbox[3] - prev_bbox[1]
        new_width = new_bbox[2] - new_bbox[0]
        new_height = new_bbox[3] - new_bbox[1]

        # Calculate size change ratios
        width_ratio = new_width / prev_width if prev_width > 0 else 1.0
        height_ratio = new_height / prev_height if prev_height > 0 else 1.0

        # VERY AGGRESSIVE size change limits per frame
        max_change = 1.0 + self.max_size_change_per_frame  # 1.15
        min_change = 1.0 - self.max_size_change_per_frame  # 0.85

        # Clip to prevent extreme changes
        width_ratio = np.clip(width_ratio, min_change, max_change)
        height_ratio = np.clip(height_ratio, min_change, max_change)

        # VERY AGGRESSIVE smoothing - heavily favor previous size
        target_width = prev_width * width_ratio
        target_height = prev_height * height_ratio

        smoothed_width = prev_width * (1 - self.size_smoothing_factor) + target_width * self.size_smoothing_factor
        smoothed_height = prev_height * (1 - self.size_smoothing_factor) + target_height * self.size_smoothing_factor

        # Additional constraint: don't allow size to change more than 10% from previous
        final_width_ratio = smoothed_width / prev_width
        final_height_ratio = smoothed_height / prev_height

        if final_width_ratio > 1.1 or final_width_ratio < 0.9:
            smoothed_width = prev_width * np.clip(final_width_ratio, 0.9, 1.1)

        if final_height_ratio > 1.1 or final_height_ratio < 0.9:
            smoothed_height = prev_height * np.clip(final_height_ratio, 0.9, 1.1)

        # Calculate new bbox with smoothed size, keeping center position from new detection
        center_x = (new_bbox[0] + new_bbox[2]) / 2
        center_y = (new_bbox[1] + new_bbox[3]) / 2

        smoothed_bbox = [
            int(center_x - smoothed_width / 2),
            int(center_y - smoothed_height / 2),
            int(center_x + smoothed_width / 2),
            int(center_y + smoothed_height / 2)
        ]

        return smoothed_bbox

    def predict_next_position(self, track: Dict) -> List[int]:
        """
        Predict next position based on track history to improve tracking continuity
        """
        if 'position_history' not in track or len(track['position_history']) < 2:
            return track['bbox']

        # Get last two positions
        current_pos = track['position_history'][-1]
        prev_pos = track['position_history'][-2]

        # Calculate velocity
        velocity_x = current_pos[0] - prev_pos[0]
        velocity_y = current_pos[1] - prev_pos[1]

        # Predict next center position
        predicted_center_x = current_pos[0] + velocity_x
        predicted_center_y = current_pos[1] + velocity_y

        # Use current bbox size
        current_bbox = track['bbox']
        width = current_bbox[2] - current_bbox[0]
        height = current_bbox[3] - current_bbox[1]

        # Create predicted bbox
        predicted_bbox = [
            int(predicted_center_x - width / 2),
            int(predicted_center_y - height / 2),
            int(predicted_center_x + width / 2),
            int(predicted_center_y + height / 2)
        ]

        return predicted_bbox

    def update_tracks(self, detections: List[Dict]) -> List[Dict]:
        """
        Update object tracks with new detections
        Enhanced with spatial/temporal validation to prevent teleportation
        """
        self.frame_count += 1
        frame_height, frame_width = self.current_frame.shape[:2]

        if len(detections) == 0:
            # No detections - IMPROVED HANDLING TO PREVENT FREEZING
            updated_tracks = []
            for track_id, track in self.active_tracks.items():
                track['frames_since_update'] += 1

                # MUCH MORE AGGRESSIVE TIMEOUT TO PREVENT FREEZING
                if track['frames_since_update'] < self.track_timeout:
                    # Less aggressive confidence decay to maintain tracking
                    decay_factor = 0.98 ** track['frames_since_update']  # Slower decay
                    track['confidence'] = max(track['confidence'] * decay_factor, 0.1)  # Minimum confidence

                    # Predict next position to help with tracking continuity
                    if track['frames_since_update'] <= 5:  # Only predict for recent tracks
                        predicted_bbox = self.predict_next_position(track)
                        track['predicted_bbox'] = predicted_bbox

                    # Update classification confidence if available
                    if 'product_classification' in track:
                        track['product_classification']['classification_confidence'] = max(
                            track['product_classification']['classification_confidence'] * decay_factor, 0.1)

                    updated_tracks.append(track)
                else:
                    logger.debug(f"Track {track_id} timed out after {track['frames_since_update']} frames")

            self.active_tracks = {track['track_id']: track for track in updated_tracks}
            return updated_tracks

        # We have exactly 1 detection (enforced in detect_hands_products)
        detection = detections[0]

        # IMPROVED TRACK MATCHING TO PREVENT FREEZING
        best_track = None
        best_distance = float('inf')
        valid_transitions = []

        for track_id, track in self.active_tracks.items():
            # Try both current bbox and predicted bbox for better tracking
            current_bbox = track['bbox']
            predicted_bbox = track.get('predicted_bbox', current_bbox)

            # Calculate distances to both positions
            distance_current = self.calculate_distance(current_bbox, detection['bbox'])
            distance_predicted = self.calculate_distance(predicted_bbox, detection['bbox'])

            # Use the better distance
            distance = min(distance_current, distance_predicted)

            # MORE LENIENT distance check to prevent freezing
            if distance < self.max_tracking_distance:
                # Relaxed spatial/temporal validation for better continuity
                is_valid, reason = self.validate_track_transition(
                    current_bbox, detection['bbox'], frame_height, frame_width)

                # If strict validation fails, try with predicted position
                if not is_valid and 'predicted_bbox' in track:
                    is_valid, reason = self.validate_track_transition(
                        predicted_bbox, detection['bbox'], frame_height, frame_width)
                    if is_valid:
                        reason = f"predicted_position_{reason}"

                if is_valid:
                    valid_transitions.append((track, distance))
                else:
                    logger.debug(f"Track {track_id} rejected: {reason}")

        # Choose best valid transition
        if valid_transitions:
            valid_transitions.sort(key=lambda x: x[1])  # Sort by distance
            best_track, best_distance = valid_transitions[0]

            # SMOOTH SIZE CHANGES TO PREVENT JUMPING
            prev_bbox = best_track['bbox'].copy()
            raw_new_bbox = detection['bbox']

            # Apply size smoothing
            smoothed_bbox = self.smooth_bbox_size(prev_bbox, raw_new_bbox)

            # Update track with smoothed bbox
            best_track['bbox'] = smoothed_bbox
            best_track['confidence'] = detection['confidence']
            best_track['class_name'] = detection['class_name']
            best_track['age'] += 1
            best_track['frames_since_update'] = 0

            # Update position history for prediction
            center_x = (smoothed_bbox[0] + smoothed_bbox[2]) / 2
            center_y = (smoothed_bbox[1] + smoothed_bbox[3]) / 2

            if 'position_history' not in best_track:
                best_track['position_history'] = []
            best_track['position_history'].append((center_x, center_y))

            # Keep only last 5 positions for prediction
            if len(best_track['position_history']) > 5:
                best_track['position_history'] = best_track['position_history'][-5:]

            # Add movement statistics
            movement_distance = self.calculate_distance(prev_bbox, smoothed_bbox)
            best_track['movement_distance'] = movement_distance
            best_track['validation_status'] = 'valid_transition_smoothed'

            # Clear predicted bbox since we have a real detection
            if 'predicted_bbox' in best_track:
                del best_track['predicted_bbox']

            # Add DYNAMIC product classification
            if self.yolo_session:
                classification = self.classify_product_in_bbox(
                    self.current_frame, smoothed_bbox)
                if classification:
                    best_track['product_classification'] = classification

            # Keep only this track (prevent multiple tracks)
            self.active_tracks = {best_track['track_id']: best_track}
            return [best_track]
        else:
            # No valid existing tracks - create new track ONLY if detection is valid
            # AGGRESSIVE filtering for new tracks
            is_invalid, invalid_reason = self.is_invalid_detection(
                detection['bbox'], frame_height, frame_width)

            if not is_invalid:
                new_track = {
                    'track_id': self.next_track_id,
                    'bbox': detection['bbox'],
                    'confidence': detection['confidence'],
                    'class_name': detection['class_name'],
                    'age': 1,
                    'frames_since_update': 0,
                    'source': detection['source'],
                    'movement_distance': 0,
                    'validation_status': 'new_track_validated',
                    'filter_status': 'passed_aggressive_filter'
                }

                # Initialize position history
                center_x = (detection['bbox'][0] + detection['bbox'][2]) / 2
                center_y = (detection['bbox'][1] + detection['bbox'][3]) / 2
                new_track['position_history'] = [(center_x, center_y)]

                # Add DYNAMIC product classification
                if self.yolo_session:
                    classification = self.classify_product_in_bbox(
                        self.current_frame, detection['bbox'])
                    if classification:
                        new_track['product_classification'] = classification

                self.next_track_id += 1

                # Keep only this new track
                self.active_tracks = {new_track['track_id']: new_track}
                return [new_track]
            else:
                logger.debug(f"New detection rejected: {invalid_reason}")
                # Keep existing tracks without update
                updated_tracks = []
                for track_id, track in self.active_tracks.items():
                    track['frames_since_update'] += 1
                    if track['frames_since_update'] < self.track_timeout:
                        track['confidence'] *= 0.95
                        updated_tracks.append(track)

                self.active_tracks = {track['track_id']: track for track in updated_tracks}
                return updated_tracks

    def process_video(self, video_path: str, output_path: str) -> Dict:
        """
        Process video with improved hand-product tracking
        """
        logger.info(f"🎯 Processing video with improved hand-product tracking: {video_path}")

        # Reset tracking state
        self.active_tracks = {}
        self.next_track_id = 0
        self.frame_count = 0
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(detectShadows=False)

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")

        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Create output video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        # Processing statistics
        total_detections = 0
        unique_tracks = set()
        confidence_scores = []

        logger.info(f"   Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")

        frame_num = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Store current frame for classification
            self.current_frame = frame.copy()

            # Detect hands and products
            detections = self.detect_hands_products(frame)

            # Update tracks
            tracks = self.update_tracks(detections)

            # Update statistics
            total_detections += len(detections)
            for track in tracks:
                unique_tracks.add(track['track_id'])
                confidence_scores.append(track['confidence'])

            # Draw results on frame with enhanced information
            for track in tracks:
                bbox = track['bbox']
                confidence = track['confidence']
                track_id = track['track_id']

                # Color coding based on validation status
                validation_status = track.get('validation_status', 'unknown')
                if validation_status == 'valid_transition':
                    color = (0, 255, 0)  # Green for valid transitions
                elif validation_status == 'new_track':
                    color = (0, 255, 255)  # Yellow for new tracks
                else:
                    color = (255, 0, 0)  # Red for issues

                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)

                # Enhanced track info with dynamic classification
                label = f"Track {track_id}: {confidence:.3f}"
                if 'product_classification' in track:
                    product_info = track['product_classification']
                    # Show DYNAMIC confidence (not static 0.850)
                    class_conf = product_info['classification_confidence']
                    label += f" | {product_info['product_class']} ({class_conf:.3f})"

                cv2.putText(frame, label, (bbox[0], bbox[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                # Movement and validation info
                movement_dist = track.get('movement_distance', 0)
                frames_since_update = track.get('frames_since_update', 0)
                status_label = f"Move: {movement_dist:.1f}px | Age: {frames_since_update}"
                cv2.putText(frame, status_label, (bbox[0], bbox[3] + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                # Validation status
                validation_label = f"Status: {validation_status}"
                cv2.putText(frame, validation_label, (bbox[0], bbox[3] + 40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # Add frame info
            info_text = f"Frame: {frame_num}, Detections: {len(detections)}, Tracks: {len(tracks)}"
            cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            out.write(frame)
            frame_num += 1

            if frame_num % 50 == 0:
                logger.info(f"   Processed {frame_num}/{total_frames} frames")

        cap.release()
        out.release()

        # Calculate final statistics
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0

        result = {
            'video_path': video_path,
            'output_path': output_path,
            'total_frames': frame_num,
            'total_detections': total_detections,
            'unique_tracks': len(unique_tracks),
            'avg_confidence': avg_confidence,
            'detection_rate': total_detections / frame_num if frame_num > 0 else 0,
            'method': 'improved_hand_product_tracking'
        }

        logger.info(f"✅ Improved tracking completed:")
        logger.info(f"   Total detections: {total_detections}")
        logger.info(f"   Unique tracks: {len(unique_tracks)}")
        logger.info(f"   Average confidence: {avg_confidence:.3f}")
        logger.info(f"   Output saved to: {output_path}")

        return result
