#!/usr/bin/env python3
"""
Improved Hand-Product Tracker with Separated Tracking and Classification
- Uses hand-to-product model for robust tracking (prevents box disappearing)
- Uses YOLO model only within bounding box for accurate product classification
"""

import cv2
import numpy as np
import onnxruntime as ort
import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedHandProductTracker:
    """
    Improved tracker that separates hand-product tracking from product classification
    """
    
    def __init__(self, 
                 hand_model_path: str = "yolow-l_product_and_hand_detector.onnx",
                 yolo_model_path: str = None):
        """
        Initialize improved tracker with separate models
        
        Args:
            hand_model_path: Path to hand-to-product detection model (for tracking)
            yolo_model_path: Path to YOLO model for product classification (optional)
        """
        self.hand_model_path = hand_model_path
        self.yolo_model_path = yolo_model_path
        
        # Results directory
        self.results_dir = Path("improved_tracking_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Tracking parameters
        self.confidence_threshold = 0.15  # Lower for hand model (more sensitive)
        self.nms_threshold = 0.4
        self.max_tracking_distance = 150  # Increased for better tracking
        self.track_timeout = 15  # Increased to prevent disappearing boxes
        
        # Current tracking state
        self.active_tracks = {}
        self.next_track_id = 0
        self.frame_count = 0
        
        # Motion detection for filtering stationary objects
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(detectShadows=False)
        self.motion_threshold = 300  # Reduced for more sensitive motion detection
        
        # Load models
        self.hand_session = None
        self.yolo_session = None
        self.load_models()
        
    def load_models(self):
        """Load both hand tracking and YOLO classification models"""
        
        # Load hand-to-product tracking model (primary)
        logger.info(f"Loading hand tracking model: {self.hand_model_path}")
        if not os.path.exists(self.hand_model_path):
            raise FileNotFoundError(f"Hand model not found: {self.hand_model_path}")
        
        self.hand_session = ort.InferenceSession(self.hand_model_path)
        self.hand_input_name = self.hand_session.get_inputs()[0].name
        self.hand_output_names = [output.name for output in self.hand_session.get_outputs()]
        self.hand_input_shape = self.hand_session.get_inputs()[0].shape
        
        logger.info("✅ Hand tracking model loaded successfully")
        logger.info(f"   Input shape: {self.hand_input_shape}")
        logger.info(f"   Input name: {self.hand_input_name}")
        logger.info(f"   Output names: {self.hand_output_names}")
        
        # Load YOLO classification model (optional)
        if self.yolo_model_path and os.path.exists(self.yolo_model_path):
            logger.info(f"Loading YOLO classification model: {self.yolo_model_path}")
            self.yolo_session = ort.InferenceSession(self.yolo_model_path)
            self.yolo_input_name = self.yolo_session.get_inputs()[0].name
            self.yolo_output_names = [output.name for output in self.yolo_session.get_outputs()]
            self.yolo_input_shape = self.yolo_session.get_inputs()[0].shape
            
            logger.info("✅ YOLO classification model loaded successfully")
            logger.info(f"   Input shape: {self.yolo_input_shape}")
        else:
            logger.info("ℹ️  No YOLO classification model provided - using hand model only")
    
    def preprocess_frame(self, frame: np.ndarray, target_size: Tuple[int, int] = (640, 640)) -> np.ndarray:
        """Preprocess frame for ONNX model input"""
        height, width = target_size
        resized = cv2.resize(frame, (width, height))
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        normalized = rgb.astype(np.float32) / 255.0
        input_data = np.transpose(normalized, (2, 0, 1))
        input_data = np.expand_dims(input_data, axis=0)
        return input_data
    
    def detect_motion_areas(self, frame: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect areas of motion in the frame using background subtraction"""
        # Apply background subtraction
        fg_mask = self.background_subtractor.apply(frame)
        
        # Morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
        
        # Find contours of moving objects
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_areas = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.motion_threshold:  # Filter out small motion noise
                x, y, w, h = cv2.boundingRect(contour)
                motion_areas.append((x, y, x + w, y + h))
        
        return motion_areas
    
    def calculate_overlap(self, bbox1: Tuple[int, int, int, int], 
                         bbox2: Tuple[int, int, int, int]) -> float:
        """Calculate overlap ratio between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        
        return intersection / area1 if area1 > 0 else 0.0
    
    def is_moving_detection(self, bbox: Tuple[int, int, int, int], 
                           motion_areas: List[Tuple[int, int, int, int]]) -> bool:
        """Check if detection overlaps with motion areas"""
        for motion_area in motion_areas:
            overlap = self.calculate_overlap(bbox, motion_area)
            if overlap > 0.2:  # Reduced threshold for more sensitive detection
                return True
        return False
    
    def detect_hands_products(self, frame: np.ndarray) -> List[Dict]:
        """Detect hands and products using the hand-to-product model"""
        # Detect motion areas first
        motion_areas = self.detect_motion_areas(frame)
        
        # Preprocess frame for hand model
        input_data = self.preprocess_frame(frame)
        
        # Run hand model inference
        outputs = self.hand_session.run(self.hand_output_names, {self.hand_input_name: input_data})
        
        # Parse outputs
        num_dets, boxes, scores, labels = outputs[:4]
        
        # Get number of detections
        if len(num_dets.shape) > 0:
            num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
        else:
            num_detections = int(num_dets)
        
        detections = []
        orig_h, orig_w = frame.shape[:2]
        
        # Process detections
        for i in range(min(num_detections, boxes.shape[1] if len(boxes.shape) > 1 else 0)):
            if i < boxes.shape[1]:
                box = boxes[0, i]
                score = scores[0, i]
                label = labels[0, i]
                
                confidence = float(score)
                class_id = int(label)
                
                if confidence > self.confidence_threshold and class_id >= 0:
                    x1, y1, x2, y2 = box
                    
                    # Scale to original frame size
                    x1 = int(x1 * orig_w / 640)
                    y1 = int(y1 * orig_h / 640)
                    x2 = int(x2 * orig_w / 640)
                    y2 = int(y2 * orig_h / 640)
                    
                    # Ensure valid bounding box
                    if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                        bbox = (x1, y1, x2, y2)
                        
                        # Filter based on motion (only keep moving objects)
                        if self.is_moving_detection(bbox, motion_areas):
                            detections.append({
                                'bbox': [x1, y1, x2, y2],
                                'confidence': confidence,
                                'class_id': class_id,
                                'class_name': 'product_in_hand',
                                'source': 'hand_model'
                            })
        
        # Apply NMS and keep only the best detection
        detections = self.apply_nms(detections)
        
        # CRITICAL: Only keep the SINGLE highest confidence detection
        if len(detections) > 1:
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            detections = [detections[0]]
        
        return detections
    
    def classify_product_in_bbox(self, frame: np.ndarray, bbox: List[int]) -> Optional[Dict]:
        """
        Classify product within the given bounding box using YOLO model
        DYNAMIC classification per frame - confidence varies based on actual detection
        """
        if not self.yolo_session:
            return None

        x1, y1, x2, y2 = bbox

        # Ensure valid ROI bounds
        frame_h, frame_w = frame.shape[:2]
        x1 = max(0, min(x1, frame_w))
        y1 = max(0, min(y1, frame_h))
        x2 = max(x1, min(x2, frame_w))
        y2 = max(y1, min(y2, frame_h))

        # Extract region of interest (ROI) from the bounding box
        roi = frame[y1:y2, x1:x2]

        if roi.size == 0 or roi.shape[0] < 10 or roi.shape[1] < 10:
            return None

        try:
            # Preprocess ROI for YOLO model
            input_data = self.preprocess_frame(roi)

            # Run YOLO inference on ROI only
            outputs = self.yolo_session.run(self.yolo_output_names, {self.yolo_input_name: input_data})

            # Parse YOLO outputs for actual classification
            num_dets, boxes, scores, labels = outputs[:4]

            # Get number of detections
            if len(num_dets.shape) > 0:
                num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
            else:
                num_detections = int(num_dets)

            best_confidence = 0.0
            best_class = 'unknown'

            # Find best detection within ROI
            for i in range(min(num_detections, boxes.shape[1] if len(boxes.shape) > 1 else 0)):
                if i < boxes.shape[1]:
                    score = scores[0, i]
                    label = labels[0, i]

                    confidence = float(score)
                    class_id = int(label)

                    if confidence > best_confidence and confidence > 0.2:
                        best_confidence = confidence
                        # Map class_id to product name (simplified for amino grape)
                        if class_id == 0:  # Assuming class 0 is product
                            best_class = 'amino_grape'
                        else:
                            best_class = f'product_class_{class_id}'

            if best_confidence > 0.2:
                return {
                    'product_class': best_class,
                    'classification_confidence': best_confidence,  # DYNAMIC confidence
                    'method': 'yolo_roi',
                    'roi_size': f"{roi.shape[1]}x{roi.shape[0]}",
                    'frame_number': self.frame_count
                }

        except Exception as e:
            logger.warning(f"Classification error: {e}")

        return None
    
    def apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to remove duplicate detections"""
        if len(detections) <= 1:
            return detections
        
        # Convert to format needed for NMS
        boxes = np.array([d['bbox'] for d in detections], dtype=np.float32)
        scores = np.array([d['confidence'] for d in detections], dtype=np.float32)
        
        # Apply NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(), 
                                  self.confidence_threshold, self.nms_threshold)
        
        if len(indices) > 0:
            indices = indices.flatten()
            return [detections[i] for i in indices]
        
        return []

    def calculate_distance(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate distance between two bounding box centers"""
        center1_x = (bbox1[0] + bbox1[2]) / 2
        center1_y = (bbox1[1] + bbox1[3]) / 2
        center2_x = (bbox2[0] + bbox2[2]) / 2
        center2_y = (bbox2[1] + bbox2[3]) / 2

        return np.sqrt((center1_x - center2_x)**2 + (center1_y - center2_y)**2)

    def is_valid_track_update(self, current_bbox: List[int], new_bbox: List[int]) -> bool:
        """
        Validate if a new detection can update an existing track
        Prevents teleportation and sudden size changes (face detection issue)
        """
        if not current_bbox or not new_bbox:
            return True

        # Calculate current and new bbox properties
        curr_x1, curr_y1, curr_x2, curr_y2 = current_bbox
        new_x1, new_y1, new_x2, new_y2 = new_bbox

        curr_w, curr_h = curr_x2 - curr_x1, curr_y2 - curr_y1
        new_w, new_h = new_x2 - new_x1, new_y2 - new_y1

        curr_center_x = (curr_x1 + curr_x2) / 2
        curr_center_y = (curr_y1 + curr_y2) / 2
        new_center_x = (new_x1 + new_x2) / 2
        new_center_y = (new_y1 + new_y2) / 2

        # 1. Distance constraint - prevent teleportation
        center_distance = np.sqrt((curr_center_x - new_center_x)**2 + (curr_center_y - new_center_y)**2)
        max_movement = max(curr_w, curr_h) * 0.8  # Max movement is 80% of bbox size

        if center_distance > max_movement:
            return False  # Too far - likely teleportation/face detection

        # 2. Size constraint - prevent sudden size changes
        size_ratio_w = new_w / curr_w if curr_w > 0 else 1.0
        size_ratio_h = new_h / curr_h if curr_h > 0 else 1.0

        # Allow size changes between 0.5x and 2.0x (reasonable for product movement)
        if size_ratio_w < 0.5 or size_ratio_w > 2.0 or size_ratio_h < 0.5 or size_ratio_h > 2.0:
            return False  # Too dramatic size change

        # 3. Aspect ratio constraint - products shouldn't change shape dramatically
        curr_aspect = curr_w / curr_h if curr_h > 0 else 1.0
        new_aspect = new_w / new_h if new_h > 0 else 1.0
        aspect_change = abs(curr_aspect - new_aspect) / curr_aspect if curr_aspect > 0 else 0

        if aspect_change > 0.5:  # More than 50% aspect ratio change
            return False  # Likely different object (face vs product)

        return True

    def update_tracks(self, detections: List[Dict]) -> List[Dict]:
        """
        Update object tracks with new detections
        Enhanced with teleportation and face detection prevention
        """
        self.frame_count += 1

        if len(detections) == 0:
            # No detections - age existing tracks but keep them longer
            updated_tracks = []
            for track_id, track in self.active_tracks.items():
                track['frames_since_update'] += 1
                # Increased timeout to prevent disappearing boxes
                if track['frames_since_update'] < self.track_timeout:
                    # Reduce confidence gradually when no detection
                    track['confidence'] *= 0.95
                    updated_tracks.append(track)

            self.active_tracks = {track['track_id']: track for track in updated_tracks}
            return updated_tracks

        # We have exactly 1 detection (enforced in detect_hands_products)
        detection = detections[0]

        # Check if we can update an existing track
        best_track = None
        best_distance = float('inf')

        for track_id, track in self.active_tracks.items():
            distance = self.calculate_distance(track['bbox'], detection['bbox'])

            # CRITICAL: Validate track update to prevent teleportation/face detection
            if (distance < best_distance and
                distance < self.max_tracking_distance and
                self.is_valid_track_update(track['bbox'], detection['bbox'])):
                best_distance = distance
                best_track = track

        if best_track:
            # Update existing track with validation
            best_track['bbox'] = detection['bbox']
            best_track['confidence'] = detection['confidence']
            best_track['class_name'] = detection['class_name']
            best_track['age'] += 1
            best_track['frames_since_update'] = 0

            # Add product classification if available (DYNAMIC per frame)
            if self.yolo_session:
                classification = self.classify_product_in_bbox(
                    self.current_frame, detection['bbox'])
                if classification:
                    best_track['product_classification'] = classification

            # Keep only this track (prevent multiple tracks)
            self.active_tracks = {best_track['track_id']: best_track}
            return [best_track]
        else:
            # Create new track only if no valid existing track
            new_track = {
                'track_id': self.next_track_id,
                'bbox': detection['bbox'],
                'confidence': detection['confidence'],
                'class_name': detection['class_name'],
                'age': 1,
                'frames_since_update': 0,
                'source': detection['source']
            }

            # Add product classification if available (DYNAMIC per frame)
            if self.yolo_session:
                classification = self.classify_product_in_bbox(
                    self.current_frame, detection['bbox'])
                if classification:
                    new_track['product_classification'] = classification

            self.next_track_id += 1

            # Keep only this new track
            self.active_tracks = {new_track['track_id']: new_track}
            return [new_track]

    def process_video(self, video_path: str, output_path: str) -> Dict:
        """
        Process video with improved hand-product tracking
        """
        logger.info(f"🎯 Processing video with improved hand-product tracking: {video_path}")

        # Reset tracking state
        self.active_tracks = {}
        self.next_track_id = 0
        self.frame_count = 0
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(detectShadows=False)

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")

        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Create output video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        # Processing statistics
        total_detections = 0
        unique_tracks = set()
        confidence_scores = []

        logger.info(f"   Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")

        frame_num = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Store current frame for classification
            self.current_frame = frame.copy()

            # Detect hands and products
            detections = self.detect_hands_products(frame)

            # Update tracks
            tracks = self.update_tracks(detections)

            # Update statistics
            total_detections += len(detections)
            for track in tracks:
                unique_tracks.add(track['track_id'])
                confidence_scores.append(track['confidence'])

            # Draw results on frame
            for track in tracks:
                bbox = track['bbox']
                confidence = track['confidence']
                track_id = track['track_id']

                # Draw bounding box (green for hand model)
                color = (0, 255, 0)  # Green for hand-product tracking
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)

                # Draw track info
                label = f"Track {track_id}: {confidence:.3f}"
                if 'product_classification' in track:
                    product_info = track['product_classification']
                    label += f" | {product_info['product_class']} ({product_info['classification_confidence']:.3f})"

                cv2.putText(frame, label, (bbox[0], bbox[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                # Draw source indicator
                source_label = f"Source: {track.get('source', 'unknown')}"
                cv2.putText(frame, source_label, (bbox[0], bbox[3] + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # Add frame info
            info_text = f"Frame: {frame_num}, Detections: {len(detections)}, Tracks: {len(tracks)}"
            cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            out.write(frame)
            frame_num += 1

            if frame_num % 50 == 0:
                logger.info(f"   Processed {frame_num}/{total_frames} frames")

        cap.release()
        out.release()

        # Calculate final statistics
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0

        result = {
            'video_path': video_path,
            'output_path': output_path,
            'total_frames': frame_num,
            'total_detections': total_detections,
            'unique_tracks': len(unique_tracks),
            'avg_confidence': avg_confidence,
            'detection_rate': total_detections / frame_num if frame_num > 0 else 0,
            'method': 'improved_hand_product_tracking'
        }

        logger.info(f"✅ Improved tracking completed:")
        logger.info(f"   Total detections: {total_detections}")
        logger.info(f"   Unique tracks: {len(unique_tracks)}")
        logger.info(f"   Average confidence: {avg_confidence:.3f}")
        logger.info(f"   Output saved to: {output_path}")

        return result
