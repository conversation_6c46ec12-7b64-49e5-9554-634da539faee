#!/usr/bin/env python3
"""
Amino Energy Grape SAMURAI Validation
====================================

This script specifically validates the ONNX tracking system against SAMURAI ground truth
for amino energy grape, which is the only product we have QuadCam videos for.

This provides a direct comparison between:
1. ONNX tracking on amino_energy_grape.mp4 (raw video)
2. SAMURAI tracking results (100% accurate ground truth)
"""

import json
import numpy as np
from pathlib import Path
import logging
from onnx_product_hand_tracking_system import ONNXProductHandTracker

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_amino_grape_against_samurai():
    """Validate ONNX tracking specifically for amino energy grape against SAMURAI"""
    logger.info("🎯 Amino Energy Grape: ONNX vs SAMURAI Validation")
    logger.info("=" * 60)
    
    # Initialize tracker
    tracker = ONNXProductHandTracker()
    
    # Paths
    video_name = "amino_energy_grape"
    raw_video_path = f"new_test_videos/{video_name}.mp4"
    samurai_results_path = f"new_test_videos_samurai_results/{video_name}/tracking_results.json"
    
    # Check if files exist
    if not Path(raw_video_path).exists():
        logger.error(f"Raw video not found: {raw_video_path}")
        return None
    
    if not Path(samurai_results_path).exists():
        logger.error(f"SAMURAI results not found: {samurai_results_path}")
        return None
    
    logger.info(f"📹 Raw video: {raw_video_path}")
    logger.info(f"🎯 SAMURAI ground truth: {samurai_results_path}")
    
    # Step 1: Process with ONNX tracker
    logger.info("\n" + "=" * 40)
    logger.info("STEP 1: Processing with ONNX Tracker")
    logger.info("=" * 40)
    
    output_path = tracker.results_dir / f"{video_name}_onnx_vs_samurai.mp4"
    onnx_results = tracker.process_video(raw_video_path, str(output_path))
    
    logger.info(f"✅ ONNX processing complete:")
    logger.info(f"   Total detections: {onnx_results['total_detections']}")
    logger.info(f"   Unique tracks: {onnx_results['unique_tracks']}")
    logger.info(f"   Average confidence: {onnx_results['avg_confidence']:.3f}")
    
    # Step 2: Load SAMURAI ground truth
    logger.info("\n" + "=" * 40)
    logger.info("STEP 2: Loading SAMURAI Ground Truth")
    logger.info("=" * 40)
    
    with open(samurai_results_path, 'r') as f:
        samurai_data = json.load(f)
    
    samurai_frames = len(samurai_data.get('frames', []))
    samurai_seed_frame = samurai_data.get('seed_frame', 1)
    
    logger.info(f"✅ SAMURAI data loaded:")
    logger.info(f"   Total frames: {samurai_frames}")
    logger.info(f"   Seed frame: {samurai_seed_frame}")
    logger.info(f"   Video path: {samurai_data.get('video_path', 'N/A')}")
    
    # Step 3: Frame-by-frame comparison
    logger.info("\n" + "=" * 40)
    logger.info("STEP 3: Frame-by-Frame Comparison")
    logger.info("=" * 40)
    
    comparison_results = compare_frame_by_frame(onnx_results, samurai_data)
    
    # Step 4: Calculate metrics
    logger.info("\n" + "=" * 40)
    logger.info("STEP 4: Calculating Validation Metrics")
    logger.info("=" * 40)
    
    metrics = calculate_validation_metrics(comparison_results)
    
    # Step 5: Generate report
    validation_report = {
        "video_name": video_name,
        "timestamp": str(Path().cwd()),
        "onnx_results": {
            "total_detections": onnx_results['total_detections'],
            "unique_tracks": onnx_results['unique_tracks'],
            "avg_confidence": onnx_results['avg_confidence'],
            "total_frames": onnx_results['total_frames']
        },
        "samurai_ground_truth": {
            "total_frames": samurai_frames,
            "seed_frame": samurai_seed_frame,
            "video_path": samurai_data.get('video_path', 'N/A')
        },
        "comparison_metrics": metrics,
        "frame_comparisons": comparison_results,
        "output_video": str(output_path)
    }
    
    # Save validation report
    report_file = tracker.results_dir / "amino_grape_samurai_validation.json"
    with open(report_file, 'w') as f:
        json.dump(validation_report, f, indent=2)
    
    # Print summary
    logger.info("\n" + "=" * 60)
    logger.info("🎉 AMINO GRAPE VALIDATION COMPLETE!")
    logger.info("=" * 60)
    logger.info(f"📊 Overall IoU Score: {metrics['avg_iou']:.3f}")
    logger.info(f"📈 Detection Overlap: {metrics['detection_overlap_ratio']:.1%}")
    logger.info(f"🎯 Tracking Quality: {metrics['tracking_quality']}")
    logger.info(f"📁 Report saved: {report_file}")
    logger.info(f"🎬 Output video: {output_path}")
    
    return validation_report

def compare_frame_by_frame(onnx_results, samurai_data):
    """Compare ONNX and SAMURAI results frame by frame"""
    logger.info("Performing frame-by-frame comparison...")
    
    # Create lookup for SAMURAI frames
    samurai_frames = {frame['frame_number']: frame for frame in samurai_data.get('frames', [])}
    
    comparisons = []
    total_compared = 0
    total_overlapping = 0
    iou_scores = []
    
    for frame_result in onnx_results['frame_results']:
        frame_num = frame_result['frame_number']
        
        if frame_num in samurai_frames:
            samurai_frame = samurai_frames[frame_num]
            
            onnx_tracks = frame_result['track_data']
            samurai_objects = samurai_frame.get('objects', [])
            
            frame_comparison = {
                'frame_number': frame_num,
                'onnx_detections': len(onnx_tracks),
                'samurai_detections': len(samurai_objects),
                'best_iou': 0.0,
                'has_overlap': False
            }
            
            # Calculate best IoU between ONNX and SAMURAI detections
            if len(onnx_tracks) > 0 and len(samurai_objects) > 0:
                best_iou = 0.0
                
                for onnx_track in onnx_tracks:
                    for samurai_obj in samurai_objects:
                        iou = calculate_iou(onnx_track['bbox'], samurai_obj['bbox'])
                        best_iou = max(best_iou, iou)
                
                frame_comparison['best_iou'] = best_iou
                frame_comparison['has_overlap'] = best_iou > 0.3  # 30% IoU threshold
                
                iou_scores.append(best_iou)
                if best_iou > 0.3:
                    total_overlapping += 1
            
            comparisons.append(frame_comparison)
            total_compared += 1
    
    logger.info(f"   Compared {total_compared} frames")
    logger.info(f"   Overlapping frames: {total_overlapping}")
    logger.info(f"   Average IoU: {np.mean(iou_scores):.3f}" if iou_scores else "   No IoU scores calculated")
    
    return comparisons

def calculate_validation_metrics(comparisons):
    """Calculate overall validation metrics"""
    if not comparisons:
        return {
            'avg_iou': 0.0,
            'detection_overlap_ratio': 0.0,
            'tracking_quality': 'No Data'
        }
    
    # Calculate metrics
    iou_scores = [c['best_iou'] for c in comparisons if c['best_iou'] > 0]
    overlapping_frames = sum(1 for c in comparisons if c['has_overlap'])
    
    avg_iou = np.mean(iou_scores) if iou_scores else 0.0
    detection_overlap_ratio = overlapping_frames / len(comparisons) if comparisons else 0.0
    
    # Determine tracking quality
    if avg_iou > 0.7:
        tracking_quality = "Excellent"
    elif avg_iou > 0.5:
        tracking_quality = "Good"
    elif avg_iou > 0.3:
        tracking_quality = "Fair"
    else:
        tracking_quality = "Poor"
    
    return {
        'avg_iou': avg_iou,
        'min_iou': np.min(iou_scores) if iou_scores else 0.0,
        'max_iou': np.max(iou_scores) if iou_scores else 0.0,
        'detection_overlap_ratio': detection_overlap_ratio,
        'total_frames_compared': len(comparisons),
        'overlapping_frames': overlapping_frames,
        'tracking_quality': tracking_quality
    }

def calculate_iou(bbox1, bbox2):
    """Calculate Intersection over Union (IoU) between two bounding boxes"""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # Calculate intersection
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    intersection = (x2_i - x1_i) * (y2_i - y1_i)
    
    # Calculate union
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

if __name__ == "__main__":
    validate_amino_grape_against_samurai()
