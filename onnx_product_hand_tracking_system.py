#!/usr/bin/env python3
"""
ONNX Product and Hand Detection Tracking System
==============================================

This system uses the yolow-l_product_and_hand_detector.onnx model to detect
both products and hands, providing improved tracking that follows the actual
product movement instead of persisting bounding boxes in static locations.

Key features:
1. ONNX model inference for product and hand detection
2. Proper object tracking that follows movement
3. Validation against SAMURAI ground truth
4. Comparison with existing tracking systems
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional, Any
import logging
from tqdm import tqdm
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import onnxruntime as ort
    logger.info("✅ ONNX Runtime imported successfully")
except ImportError:
    logger.error("❌ ONNX Runtime not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "onnxruntime"])
    import onnxruntime as ort
    logger.info("✅ ONNX Runtime installed and imported")


class ONNXProductHandTracker:
    """ONNX-based product and hand detection with improved tracking"""
    
    def __init__(self, onnx_model_path: str = "yolow-l_product_and_hand_detector.onnx"):
        """Initialize the ONNX tracking system"""
        self.onnx_model_path = onnx_model_path
        self.session = None
        self.input_name = None
        self.output_names = None
        self.input_shape = None
        
        # Results directory
        self.results_dir = Path("onnx_tracking_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Tracking parameters
        self.confidence_threshold = 0.2  # Lower threshold based on ONNX model output
        self.nms_threshold = 0.4
        self.max_tracking_distance = 100  # pixels
        self.track_timeout = 10  # frames
        
        # Current tracking state
        self.active_tracks = {}
        self.next_track_id = 0
        self.frame_count = 0
        
        # Load ONNX model
        self.load_onnx_model()
        
    def load_onnx_model(self):
        """Load the ONNX model for inference"""
        try:
            if not os.path.exists(self.onnx_model_path):
                logger.error(f"ONNX model not found: {self.onnx_model_path}")
                raise FileNotFoundError(f"ONNX model not found: {self.onnx_model_path}")
            
            # Create ONNX Runtime session
            self.session = ort.InferenceSession(self.onnx_model_path)
            
            # Get input/output information
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
            self.input_shape = self.session.get_inputs()[0].shape
            
            logger.info(f"✅ ONNX model loaded successfully")
            logger.info(f"   Input shape: {self.input_shape}")
            logger.info(f"   Input name: {self.input_name}")
            logger.info(f"   Output names: {self.output_names}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load ONNX model: {e}")
            raise
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for ONNX model inference"""
        # Get model input dimensions
        if len(self.input_shape) == 4:  # NCHW format
            _, channels, height, width = self.input_shape
        else:
            logger.error(f"Unexpected input shape: {self.input_shape}")
            height, width = 640, 640  # Default fallback
        
        # Resize frame to model input size
        resized = cv2.resize(frame, (width, height))
        
        # Convert BGR to RGB if needed
        if channels == 3:
            resized = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1]
        normalized = resized.astype(np.float32) / 255.0
        
        # Convert to NCHW format
        if len(self.input_shape) == 4:
            preprocessed = np.transpose(normalized, (2, 0, 1))  # HWC to CHW
            preprocessed = np.expand_dims(preprocessed, axis=0)  # Add batch dimension
        else:
            preprocessed = np.expand_dims(normalized, axis=0)
        
        return preprocessed
    
    def postprocess_detections(self, outputs: List[np.ndarray],
                             original_shape: Tuple[int, int]) -> List[Dict]:
        """Postprocess ONNX model outputs to get detections"""
        detections = []

        # Expected outputs: ['num_dets', 'boxes', 'scores', 'labels']
        if len(outputs) >= 4:
            num_dets, boxes, scores, labels = outputs[:4]

            # Get number of detections
            if len(num_dets.shape) > 0:
                num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
            else:
                num_detections = int(num_dets)

            # Process each detection
            for i in range(num_detections):
                if i < boxes.shape[1]:  # Ensure we don't exceed available detections
                    # Extract detection data
                    box = boxes[0, i] if len(boxes.shape) > 1 else boxes[i]
                    score = scores[0, i] if len(scores.shape) > 1 else scores[i]
                    label = labels[0, i] if len(labels.shape) > 1 else labels[i]

                    confidence = float(score)
                    class_id = int(label)

                    if confidence > self.confidence_threshold and class_id >= 0:
                        # Extract bounding box coordinates
                        x1, y1, x2, y2 = box

                        # Scale coordinates back to original image size
                        orig_h, orig_w = original_shape
                        model_h, model_w = self.input_shape[-2:]

                        x1 = int(x1 * orig_w / model_w)
                        y1 = int(y1 * orig_h / model_h)
                        x2 = int(x2 * orig_w / model_w)
                        y2 = int(y2 * orig_h / model_h)

                        # Ensure valid bounding box and filter out oversized detections
                        if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                            # Calculate bounding box area and frame area
                            bbox_area = (x2 - x1) * (y2 - y1)
                            frame_area = orig_w * orig_h
                            area_ratio = bbox_area / frame_area

                            # Filter out detections that are too large (likely full-frame detections)
                            # and too small (likely noise)
                            if 0.001 < area_ratio < 0.8:  # Between 0.1% and 80% of frame
                                detections.append({
                                    'bbox': [x1, y1, x2, y2],
                                    'confidence': confidence,
                                    'class_id': class_id,
                                    'class_name': self.get_class_name(class_id),
                                    'area_ratio': area_ratio
                                })

        return detections
    
    def get_class_name(self, class_id: int) -> str:
        """Get class name from class ID"""
        # Based on the ONNX model output, class 0 appears to be product/bottle
        class_names = {
            0: 'product_in_hand',
            1: 'hand',
            2: 'bottle',
            3: 'can'
        }
        return class_names.get(class_id, f'class_{class_id}')
    
    def apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to remove overlapping detections"""
        if len(detections) == 0:
            return detections
        
        # Convert to format for NMS
        boxes = []
        scores = []
        
        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            boxes.append([x1, y1, x2, y2])
            scores.append(det['confidence'])
        
        boxes = np.array(boxes, dtype=np.float32)
        scores = np.array(scores, dtype=np.float32)
        
        # Apply NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(), 
                                  self.confidence_threshold, self.nms_threshold)
        
        if len(indices) > 0:
            indices = indices.flatten()
            return [detections[i] for i in indices]
        else:
            return []
    
    def calculate_distance(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate distance between two bounding box centers"""
        x1_center = (bbox1[0] + bbox1[2]) / 2
        y1_center = (bbox1[1] + bbox1[3]) / 2
        x2_center = (bbox2[0] + bbox2[2]) / 2
        y2_center = (bbox2[1] + bbox2[3]) / 2
        
        return np.sqrt((x1_center - x2_center)**2 + (y1_center - y2_center)**2)
    
    def update_tracks(self, detections: List[Dict]) -> List[Dict]:
        """Update object tracks with new detections"""
        self.frame_count += 1
        
        # Filter for product detections (products, bottles, cans) and reasonable sizes
        product_detections = [d for d in detections if d['class_name'] in ['product_in_hand', 'bottle', 'can', 'product']]
        
        # Update existing tracks
        updated_tracks = []
        used_detections = set()
        
        for track_id, track in self.active_tracks.items():
            best_match = None
            best_distance = float('inf')
            best_idx = -1
            
            # Find closest detection to this track
            for idx, detection in enumerate(product_detections):
                if idx in used_detections:
                    continue
                
                distance = self.calculate_distance(track['bbox'], detection['bbox'])
                if distance < best_distance and distance < self.max_tracking_distance:
                    best_distance = distance
                    best_match = detection
                    best_idx = idx
            
            if best_match:
                # Update track
                track['bbox'] = best_match['bbox']
                track['confidence'] = best_match['confidence']
                track['class_name'] = best_match['class_name']
                track['age'] += 1
                track['frames_since_update'] = 0
                updated_tracks.append(track)
                used_detections.add(best_idx)
            else:
                # Track lost detection
                track['frames_since_update'] += 1
                if track['frames_since_update'] < self.track_timeout:
                    updated_tracks.append(track)
        
        # Create new tracks for unmatched detections
        for idx, detection in enumerate(product_detections):
            if idx not in used_detections:
                new_track = {
                    'track_id': self.next_track_id,
                    'bbox': detection['bbox'],
                    'confidence': detection['confidence'],
                    'class_name': detection['class_name'],
                    'age': 1,
                    'frames_since_update': 0
                }
                updated_tracks.append(new_track)
                self.next_track_id += 1
        
        # Update active tracks
        self.active_tracks = {track['track_id']: track for track in updated_tracks}
        
        return updated_tracks
    
    def detect_frame(self, frame: np.ndarray) -> List[Dict]:
        """Run detection on a single frame"""
        # Preprocess frame
        input_data = self.preprocess_frame(frame)
        
        # Run inference
        outputs = self.session.run(self.output_names, {self.input_name: input_data})
        
        # Postprocess results
        detections = self.postprocess_detections(outputs, frame.shape[:2])
        
        # Apply NMS
        detections = self.apply_nms(detections)
        
        return detections
    
    def draw_detections(self, frame: np.ndarray, tracks: List[Dict]) -> np.ndarray:
        """Draw detection results on frame"""
        for track in tracks:
            x1, y1, x2, y2 = track['bbox']
            confidence = track['confidence']
            class_name = track['class_name']
            track_id = track['track_id']
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Draw label
            label = f"{class_name} ID:{track_id} {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # Draw label background
            cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            
            # Draw label text
            cv2.putText(frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return frame

    def process_video(self, video_path: str, output_path: str) -> Dict:
        """Process a single video with ONNX-based tracking"""
        logger.info(f"Processing video: {video_path}")

        # Reset tracking state
        self.active_tracks = {}
        self.next_track_id = 0
        self.frame_count = 0

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"Failed to open video: {video_path}")
            return {"status": "error", "message": "Failed to open video"}

        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Setup video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        # Processing statistics
        frame_results = []
        total_detections = 0
        confidence_scores = []

        # Process frames
        pbar = tqdm(total=total_frames, desc=f"Processing {Path(video_path).name}")

        frame_number = 1
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Run detection
            detections = self.detect_frame(frame)

            # Update tracking
            tracks = self.update_tracks(detections)

            # Draw results
            output_frame = self.draw_detections(frame.copy(), tracks)

            # Add frame info
            info_text = f"Frame: {frame_number}/{total_frames}"
            cv2.putText(output_frame, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Write frame
            out.write(output_frame)

            # Store frame results
            frame_data = {
                'frame_number': frame_number,
                'detections': len(detections),
                'tracks': len(tracks),
                'track_data': []
            }

            for track in tracks:
                frame_data['track_data'].append({
                    'track_id': track['track_id'],
                    'bbox': track['bbox'],
                    'confidence': track['confidence'],
                    'class_name': track['class_name']
                })
                confidence_scores.append(track['confidence'])

            frame_results.append(frame_data)
            total_detections += len(tracks)

            frame_number += 1
            pbar.update(1)

        pbar.close()
        cap.release()
        out.release()

        # Calculate statistics
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.0
        min_confidence = np.min(confidence_scores) if confidence_scores else 0.0
        max_confidence = np.max(confidence_scores) if confidence_scores else 0.0

        results = {
            "video_path": video_path,
            "output_path": output_path,
            "total_frames": total_frames,
            "total_detections": total_detections,
            "unique_tracks": self.next_track_id,
            "avg_confidence": avg_confidence,
            "min_confidence": min_confidence,
            "max_confidence": max_confidence,
            "frame_results": frame_results,
            "status": "success"
        }

        logger.info(f"✅ Processed {Path(video_path).name}: {total_detections} detections, {self.next_track_id} tracks")
        return results

    def load_samurai_ground_truth(self, video_name: str) -> Optional[Dict]:
        """Load SAMURAI tracking results as ground truth"""
        samurai_file = Path(f"new_test_videos_samurai_results/{video_name}/tracking_results.json")

        if not samurai_file.exists():
            logger.warning(f"SAMURAI ground truth not found for {video_name}")
            return None

        try:
            with open(samurai_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load SAMURAI ground truth for {video_name}: {e}")
            return None

    def compare_with_samurai(self, onnx_results: Dict, samurai_data: Dict) -> Dict:
        """Compare ONNX tracking results with SAMURAI ground truth"""
        comparison = {
            'video_name': Path(onnx_results['video_path']).stem,
            'onnx_total_detections': onnx_results['total_detections'],
            'samurai_total_frames': len(samurai_data.get('frames', [])),
            'samurai_seed_frame': samurai_data.get('seed_frame', 1),
            'frame_comparisons': [],
            'bbox_accuracy_scores': [],
            'detection_overlap_ratio': 0.0
        }

        # Create frame lookup for SAMURAI data
        samurai_frames = {frame['frame_number']: frame for frame in samurai_data.get('frames', [])}

        # Compare frame by frame
        overlapping_frames = 0
        total_compared_frames = 0

        for frame_result in onnx_results['frame_results']:
            frame_num = frame_result['frame_number']

            if frame_num in samurai_frames:
                samurai_frame = samurai_frames[frame_num]

                # Calculate IoU for bounding boxes
                onnx_tracks = frame_result['track_data']
                samurai_objects = samurai_frame.get('objects', [])

                if len(onnx_tracks) > 0 and len(samurai_objects) > 0:
                    # Find best matching bounding boxes
                    best_iou = 0.0
                    for onnx_track in onnx_tracks:
                        for samurai_obj in samurai_objects:
                            iou = self.calculate_iou(onnx_track['bbox'], samurai_obj['bbox'])
                            best_iou = max(best_iou, iou)

                    comparison['bbox_accuracy_scores'].append(best_iou)
                    if best_iou > 0.5:  # Consider as overlapping if IoU > 0.5
                        overlapping_frames += 1

                total_compared_frames += 1

                comparison['frame_comparisons'].append({
                    'frame_number': frame_num,
                    'onnx_detections': len(onnx_tracks),
                    'samurai_detections': len(samurai_objects),
                    'best_iou': best_iou if 'best_iou' in locals() else 0.0
                })

        # Calculate overall metrics
        if total_compared_frames > 0:
            comparison['detection_overlap_ratio'] = overlapping_frames / total_compared_frames

        if comparison['bbox_accuracy_scores']:
            comparison['avg_bbox_accuracy'] = np.mean(comparison['bbox_accuracy_scores'])
            comparison['min_bbox_accuracy'] = np.min(comparison['bbox_accuracy_scores'])
            comparison['max_bbox_accuracy'] = np.max(comparison['bbox_accuracy_scores'])
        else:
            comparison['avg_bbox_accuracy'] = 0.0
            comparison['min_bbox_accuracy'] = 0.0
            comparison['max_bbox_accuracy'] = 0.0

        return comparison

    def calculate_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate Intersection over Union (IoU) between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # Calculate union
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0


def test_on_quadcam_videos():
    """Test ONNX tracking system on QuadCam videos"""
    logger.info("🚀 Testing ONNX tracking on QuadCam videos")

    tracker = ONNXProductHandTracker()

    # Test on QuadCam videos
    video_dir = Path("QuadCamTestVideos/AminoEnergyGrape")
    if not video_dir.exists():
        logger.error(f"QuadCam video directory not found: {video_dir}")
        return

    results = {}

    for cam_num in range(4):
        video_path = video_dir / f"cam{cam_num}.mp4"
        if video_path.exists():
            output_path = tracker.results_dir / f"quadcam_cam{cam_num}_onnx_tracking.mp4"

            logger.info(f"\n📹 Processing cam{cam_num}...")
            result = tracker.process_video(str(video_path), str(output_path))
            results[f"cam{cam_num}"] = result
        else:
            logger.warning(f"Video not found: {video_path}")

    # Save results
    results_file = tracker.results_dir / "quadcam_onnx_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"✅ QuadCam results saved to: {results_file}")
    return results


def validate_against_samurai_ground_truth():
    """Validate ONNX tracking against SAMURAI ground truth"""
    logger.info("🎯 Validating ONNX tracking against SAMURAI ground truth")

    tracker = ONNXProductHandTracker()

    # Get list of videos with SAMURAI ground truth
    samurai_dir = Path("new_test_videos_samurai_results")
    video_dir = Path("new_test_videos")

    if not samurai_dir.exists() or not video_dir.exists():
        logger.error("Required directories not found")
        return

    validation_results = {}

    # Process each video that has SAMURAI ground truth
    for samurai_video_dir in samurai_dir.iterdir():
        if samurai_video_dir.is_dir() and samurai_video_dir.name != "execution_summary.json":
            video_name = samurai_video_dir.name
            video_path = video_dir / f"{video_name}.mp4"

            if video_path.exists():
                logger.info(f"\n📹 Processing {video_name} for validation...")

                # Process with ONNX tracker
                output_path = tracker.results_dir / f"{video_name}_onnx_validation.mp4"
                onnx_results = tracker.process_video(str(video_path), str(output_path))

                # Load SAMURAI ground truth
                samurai_data = tracker.load_samurai_ground_truth(video_name)

                if samurai_data:
                    # Compare results
                    comparison = tracker.compare_with_samurai(onnx_results, samurai_data)
                    validation_results[video_name] = {
                        'onnx_results': onnx_results,
                        'samurai_comparison': comparison
                    }

                    logger.info(f"   📊 Validation metrics for {video_name}:")
                    logger.info(f"      Avg IoU: {comparison['avg_bbox_accuracy']:.3f}")
                    logger.info(f"      Detection overlap: {comparison['detection_overlap_ratio']:.3f}")
                else:
                    logger.warning(f"No SAMURAI ground truth found for {video_name}")
            else:
                logger.warning(f"Video file not found: {video_path}")

    # Save validation results
    validation_file = tracker.results_dir / "samurai_validation_results.json"
    with open(validation_file, 'w') as f:
        json.dump(validation_results, f, indent=2)

    logger.info(f"✅ Validation results saved to: {validation_file}")
    return validation_results


def generate_comparison_report(validation_results: Dict):
    """Generate comprehensive comparison report"""
    logger.info("📋 Generating comparison report")

    report = {
        "timestamp": str(datetime.now()),
        "system_comparison": "ONNX Product-Hand Detector vs SAMURAI Ground Truth",
        "total_videos_compared": len(validation_results),
        "overall_metrics": {},
        "per_video_results": {},
        "summary": {}
    }

    # Calculate overall metrics
    all_iou_scores = []
    all_overlap_ratios = []

    for video_name, result in validation_results.items():
        comparison = result['samurai_comparison']

        if comparison['bbox_accuracy_scores']:
            all_iou_scores.extend(comparison['bbox_accuracy_scores'])

        all_overlap_ratios.append(comparison['detection_overlap_ratio'])

        # Per-video summary
        report['per_video_results'][video_name] = {
            'onnx_detections': comparison['onnx_total_detections'],
            'samurai_frames': comparison['samurai_total_frames'],
            'avg_iou': comparison['avg_bbox_accuracy'],
            'detection_overlap': comparison['detection_overlap_ratio'],
            'performance_rating': 'Excellent' if comparison['avg_bbox_accuracy'] > 0.7 else
                                'Good' if comparison['avg_bbox_accuracy'] > 0.5 else
                                'Fair' if comparison['avg_bbox_accuracy'] > 0.3 else 'Poor'
        }

    # Overall metrics
    if all_iou_scores:
        report['overall_metrics'] = {
            'average_iou': np.mean(all_iou_scores),
            'median_iou': np.median(all_iou_scores),
            'min_iou': np.min(all_iou_scores),
            'max_iou': np.max(all_iou_scores),
            'average_detection_overlap': np.mean(all_overlap_ratios),
            'videos_with_good_tracking': sum(1 for r in report['per_video_results'].values()
                                           if r['avg_iou'] > 0.5),
            'tracking_success_rate': sum(1 for r in report['per_video_results'].values()
                                       if r['avg_iou'] > 0.5) / len(validation_results)
        }

    # Summary
    report['summary'] = {
        'system_performance': 'Excellent' if report['overall_metrics'].get('average_iou', 0) > 0.7 else
                            'Good' if report['overall_metrics'].get('average_iou', 0) > 0.5 else
                            'Needs Improvement',
        'key_findings': [
            f"Average IoU: {report['overall_metrics'].get('average_iou', 0):.3f}",
            f"Tracking success rate: {report['overall_metrics'].get('tracking_success_rate', 0):.1%}",
            f"Videos processed: {len(validation_results)}"
        ]
    }

    # Save report
    report_file = Path("onnx_tracking_results/onnx_vs_samurai_comparison_report.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)

    logger.info(f"✅ Comparison report saved to: {report_file}")
    return report


def main():
    """Main function to run complete ONNX tracking validation"""
    logger.info("🚀 ONNX Product-Hand Tracking System - Complete Validation")
    logger.info("=" * 70)

    try:
        # Step 1: Test on QuadCam videos
        logger.info("\n" + "=" * 50)
        logger.info("STEP 1: Testing on QuadCam Videos")
        logger.info("=" * 50)
        quadcam_results = test_on_quadcam_videos()

        # Step 2: Validate against SAMURAI ground truth
        logger.info("\n" + "=" * 50)
        logger.info("STEP 2: Validating against SAMURAI Ground Truth")
        logger.info("=" * 50)
        validation_results = validate_against_samurai_ground_truth()

        # Step 3: Generate comparison report
        logger.info("\n" + "=" * 50)
        logger.info("STEP 3: Generating Comparison Report")
        logger.info("=" * 50)
        comparison_report = generate_comparison_report(validation_results)

        # Final summary
        logger.info("\n" + "=" * 70)
        logger.info("🎉 ONNX TRACKING VALIDATION COMPLETE!")
        logger.info("=" * 70)
        logger.info(f"📊 Overall Performance: {comparison_report['summary']['system_performance']}")
        logger.info(f"📈 Average IoU: {comparison_report['overall_metrics'].get('average_iou', 0):.3f}")
        logger.info(f"✅ Success Rate: {comparison_report['overall_metrics'].get('tracking_success_rate', 0):.1%}")
        logger.info(f"📁 Results saved in: onnx_tracking_results/")

    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
