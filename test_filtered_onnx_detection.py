#!/usr/bin/env python3
"""
Test the updated ONNX system with person/face filtering
"""

from onnx_product_hand_tracking_system import ONNXProductHandTracker
from pathlib import Path
import json

def test_filtered_detection():
    """Test ONNX tracking with person/face filtering"""
    
    tracker = ONNXProductHandTracker()
    
    # Test on single video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    output_path = "onnx_tracking_results/test_filtered_cam0_tracking.mp4"
    
    print(f"Testing filtered ONNX tracking on: {video_path}")
    print(f"Output will be saved to: {output_path}")
    
    # Process video
    result = tracker.process_video(video_path, output_path)
    
    # Save results
    results_file = "onnx_tracking_results/test_filtered_results.json"
    with open(results_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\nFiltered Results:")
    print(f"  Total detections: {result['total_detections']}")
    print(f"  Unique tracks: {result['unique_tracks']}")
    print(f"  Average confidence: {result['avg_confidence']:.3f}")
    print(f"  Results saved to: {results_file}")
    
    # Compare with previous results
    try:
        with open("onnx_tracking_results/test_cam0_results.json", 'r') as f:
            old_results = json.load(f)
        
        print(f"\nComparison with previous (unfiltered) results:")
        print(f"  Previous detections: {old_results['total_detections']}")
        print(f"  New detections: {result['total_detections']}")
        print(f"  Reduction: {old_results['total_detections'] - result['total_detections']} detections")
        print(f"  Reduction %: {((old_results['total_detections'] - result['total_detections']) / old_results['total_detections'] * 100):.1f}%")
        
    except FileNotFoundError:
        print("No previous results found for comparison")
    
    return result

if __name__ == "__main__":
    test_filtered_detection()
