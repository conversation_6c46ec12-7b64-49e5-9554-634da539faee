#!/usr/bin/env python3
"""
Debug the filtered ONNX detections to see what's being detected
"""

import cv2
import numpy as np
import onnxruntime as ort
from pathlib import Path

def debug_filtered_detections():
    """Debug filtered ONNX detections on multiple frames"""
    
    # Load ONNX model
    model_path = "yolow-l_product_and_hand_detector.onnx"
    session = ort.InferenceSession(model_path)
    
    input_name = session.get_inputs()[0].name
    output_names = [output.name for output in session.get_outputs()]
    
    print(f"Model info:")
    print(f"  Input: {input_name}")
    print(f"  Outputs: {output_names}")
    
    # Load video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    cap = cv2.VideoCapture(video_path)
    
    frame_count = 0
    confidence_threshold = 0.2
    
    print(f"\nAnalyzing detections from {video_path}...")
    print(f"Using confidence threshold: {confidence_threshold}")
    
    def is_product_detection(x1, y1, x2, y2, frame_w, frame_h, confidence):
        """Same filtering logic as in the main system"""
        bbox_w = x2 - x1
        bbox_h = y2 - y1
        bbox_area = bbox_w * bbox_h
        frame_area = frame_w * frame_h
        area_ratio = bbox_area / frame_area
        
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        rel_center_x = center_x / frame_w
        rel_center_y = center_y / frame_h
        aspect_ratio = bbox_w / bbox_h if bbox_h > 0 else 0
        
        # Apply all filters
        if area_ratio > 0.3:  # Too large
            return False, "too_large"
        if rel_center_y < 0.2:  # Upper area
            return False, "upper_area"
        if 0.7 < aspect_ratio < 1.4 and rel_center_y < 0.4:  # Square in upper area
            return False, "square_upper"
        if area_ratio > 0.15 and rel_center_y < 0.5:  # Large in upper half
            return False, "large_upper"
        if confidence < 0.15:  # Low confidence
            return False, "low_confidence"
        if aspect_ratio < 0.2 or aspect_ratio > 5.0:  # Extreme aspect ratio
            return False, "extreme_aspect"
        if area_ratio < 0.002:  # Too small
            return False, "too_small"
        
        return True, "passed"
    
    while frame_count < 20:  # Process first 20 frames
        ret, frame = cap.read()
        if not ret:
            break
        
        # Preprocess frame
        height, width = 640, 640
        resized = cv2.resize(frame, (width, height))
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        normalized = rgb.astype(np.float32) / 255.0
        input_data = np.transpose(normalized, (2, 0, 1))
        input_data = np.expand_dims(input_data, axis=0)
        
        # Run inference
        outputs = session.run(output_names, {input_name: input_data})
        
        # Parse outputs
        num_dets, boxes, scores, labels = outputs[:4]
        
        # Get number of detections
        if len(num_dets.shape) > 0:
            num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
        else:
            num_detections = int(num_dets)
        
        print(f"\nFrame {frame_count}:")
        print(f"  Raw detections: {num_detections}")
        
        valid_detections = 0
        
        # Process detections
        for i in range(num_detections):
            if i < boxes.shape[1]:
                box = boxes[0, i]
                score = scores[0, i]
                label = labels[0, i]
                
                confidence = float(score)
                class_id = int(label)
                
                if confidence > confidence_threshold and class_id >= 0:
                    x1, y1, x2, y2 = box
                    
                    # Scale to original frame size
                    orig_h, orig_w = frame.shape[:2]
                    x1 = int(x1 * orig_w / width)
                    y1 = int(y1 * orig_h / height)
                    x2 = int(x2 * orig_w / width)
                    y2 = int(y2 * orig_h / height)
                    
                    if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                        # Calculate properties
                        bbox_w = x2 - x1
                        bbox_h = y2 - y1
                        bbox_area = bbox_w * bbox_h
                        frame_area = orig_w * orig_h
                        area_ratio = bbox_area / frame_area
                        
                        center_x = (x1 + x2) / 2
                        center_y = (y1 + y2) / 2
                        rel_center_x = center_x / orig_w
                        rel_center_y = center_y / orig_h
                        aspect_ratio = bbox_w / bbox_h if bbox_h > 0 else 0
                        
                        # Check if it passes filtering
                        passed, reason = is_product_detection(x1, y1, x2, y2, orig_w, orig_h, confidence)
                        
                        print(f"    Detection {i}: conf={confidence:.3f}, class={class_id}")
                        print(f"      bbox=[{x1},{y1},{x2},{y2}] size={bbox_w}x{bbox_h}")
                        print(f"      area_ratio={area_ratio:.4f}, center=({rel_center_x:.2f},{rel_center_y:.2f})")
                        print(f"      aspect_ratio={aspect_ratio:.2f}")
                        print(f"      filter_result: {'PASS' if passed else 'REJECT'} ({reason})")
                        
                        if passed:
                            valid_detections += 1
        
        print(f"  Valid detections after filtering: {valid_detections}")
        frame_count += 1
    
    cap.release()

if __name__ == "__main__":
    debug_filtered_detections()
