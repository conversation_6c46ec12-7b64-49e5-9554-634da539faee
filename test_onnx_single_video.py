#!/usr/bin/env python3
"""
Test ONNX model on a single video to debug output format
"""

import cv2
import numpy as np
import onnxruntime as ort
from pathlib import Path
import json

def test_onnx_model():
    """Test ONNX model on a single frame"""
    
    # Load ONNX model
    model_path = "yolow-l_product_and_hand_detector.onnx"
    session = ort.InferenceSession(model_path)
    
    # Get model info
    input_name = session.get_inputs()[0].name
    output_names = [output.name for output in session.get_outputs()]
    input_shape = session.get_inputs()[0].shape
    
    print(f"Input name: {input_name}")
    print(f"Input shape: {input_shape}")
    print(f"Output names: {output_names}")
    
    # Load a test video frame
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    cap = cv2.VideoCapture(video_path)
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        print("Failed to read frame")
        return
    
    print(f"Original frame shape: {frame.shape}")
    
    # Preprocess frame
    height, width = 640, 640  # Model input size
    resized = cv2.resize(frame, (width, height))
    rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    normalized = rgb.astype(np.float32) / 255.0
    input_data = np.transpose(normalized, (2, 0, 1))  # HWC to CHW
    input_data = np.expand_dims(input_data, axis=0)  # Add batch dimension
    
    print(f"Input data shape: {input_data.shape}")
    
    # Run inference
    outputs = session.run(output_names, {input_name: input_data})
    
    # Analyze outputs
    for i, (name, output) in enumerate(zip(output_names, outputs)):
        print(f"\nOutput {i} ({name}):")
        print(f"  Shape: {output.shape}")
        print(f"  Type: {output.dtype}")
        if output.size < 20:  # Only print small outputs
            print(f"  Values: {output}")
        else:
            print(f"  Min: {np.min(output)}, Max: {np.max(output)}, Mean: {np.mean(output)}")
    
    # Try to extract detections
    if len(outputs) >= 4:
        num_dets, boxes, scores, labels = outputs[:4]
        
        print(f"\nDetection Analysis:")
        print(f"Number of detections: {num_dets}")
        print(f"Boxes shape: {boxes.shape}")
        print(f"Scores shape: {scores.shape}")
        print(f"Labels shape: {labels.shape}")
        
        # Process detections
        if len(num_dets.shape) > 0:
            num_detections = int(num_dets[0]) if num_dets.size > 0 else 0
        else:
            num_detections = int(num_dets)
        
        print(f"Actual number of detections: {num_detections}")
        
        if num_detections > 0:
            print(f"\nFirst few detections:")
            for i in range(min(5, num_detections)):
                if i < boxes.shape[1]:
                    box = boxes[0, i] if len(boxes.shape) > 1 else boxes[i]
                    score = scores[0, i] if len(scores.shape) > 1 else scores[i]
                    label = labels[0, i] if len(labels.shape) > 1 else labels[i]
                    
                    print(f"  Detection {i}: box={box}, score={score:.3f}, label={int(label)}")
    
    return outputs

if __name__ == "__main__":
    test_onnx_model()
