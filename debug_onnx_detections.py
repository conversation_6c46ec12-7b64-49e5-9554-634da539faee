#!/usr/bin/env python3
"""
Debug ONNX detections to see what's being detected
"""

import cv2
import numpy as np
import onnxruntime as ort
from pathlib import Path

def debug_onnx_detections():
    """Debug ONNX detections on multiple frames"""
    
    # Load ONNX model
    model_path = "yolow-l_product_and_hand_detector.onnx"
    session = ort.InferenceSession(model_path)
    
    input_name = session.get_inputs()[0].name
    output_names = [output.name for output in session.get_outputs()]
    input_shape = session.get_inputs()[0].shape
    
    print(f"Model info:")
    print(f"  Input: {input_name}, shape: {input_shape}")
    print(f"  Outputs: {output_names}")
    
    # Load video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    cap = cv2.VideoCapture(video_path)
    
    frame_count = 0
    total_detections = 0
    confidence_threshold = 0.1  # Very low threshold for debugging
    
    print(f"\nProcessing frames from {video_path}...")
    print(f"Using confidence threshold: {confidence_threshold}")
    
    while frame_count < 50:  # Process first 50 frames
        ret, frame = cap.read()
        if not ret:
            break
        
        # Preprocess frame
        height, width = 640, 640
        resized = cv2.resize(frame, (width, height))
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        normalized = rgb.astype(np.float32) / 255.0
        input_data = np.transpose(normalized, (2, 0, 1))
        input_data = np.expand_dims(input_data, axis=0)
        
        # Run inference
        outputs = session.run(output_names, {input_name: input_data})
        
        # Parse outputs
        num_dets, boxes, scores, labels = outputs[:4]
        
        # Get number of detections
        if len(num_dets.shape) > 0:
            num_detections = int(num_dets[0][0]) if num_dets.size > 0 else 0
        else:
            num_detections = int(num_dets)
        
        frame_detections = 0
        
        # Process detections
        for i in range(num_detections):
            if i < boxes.shape[1]:
                box = boxes[0, i]
                score = scores[0, i]
                label = labels[0, i]
                
                confidence = float(score)
                class_id = int(label)
                
                if confidence > confidence_threshold and class_id >= 0:
                    x1, y1, x2, y2 = box
                    
                    # Scale to original frame size
                    orig_h, orig_w = frame.shape[:2]
                    x1 = int(x1 * orig_w / width)
                    y1 = int(y1 * orig_h / height)
                    x2 = int(x2 * orig_w / width)
                    y2 = int(y2 * orig_h / height)
                    
                    if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                        frame_detections += 1
                        
                        if frame_count % 10 == 0:  # Print details every 10 frames
                            print(f"    Detection {i}: conf={confidence:.3f}, class={class_id}, bbox=[{x1},{y1},{x2},{y2}]")
        
        if frame_detections > 0 or frame_count % 10 == 0:
            print(f"Frame {frame_count}: {frame_detections} detections (total raw: {num_detections})")
        
        total_detections += frame_detections
        frame_count += 1
    
    cap.release()
    
    print(f"\nSummary:")
    print(f"  Processed {frame_count} frames")
    print(f"  Total valid detections: {total_detections}")
    print(f"  Average detections per frame: {total_detections/frame_count:.2f}")

if __name__ == "__main__":
    debug_onnx_detections()
