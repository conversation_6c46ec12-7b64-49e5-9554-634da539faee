#!/usr/bin/env python3
"""
Multi-camera fusion system with green bounding boxes and amino grape confidence only
"""

import cv2
import numpy as np
import json
import time
from pathlib import Path
from improved_hand_product_tracker import ImprovedHandProductTracker
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiCameraFusionGreen:
    def __init__(self, hand_model_path: str, yolo_model_path: str):
        """Initialize multi-camera fusion with green styling"""
        self.hand_model_path = hand_model_path
        self.yolo_model_path = yolo_model_path
        
        # Create trackers for each camera
        self.trackers = {}
        for cam_id in range(4):
            self.trackers[cam_id] = ImprovedHandProductTracker(
                hand_model_path=hand_model_path,
                yolo_model_path=yolo_model_path
            )
        
        # Fusion parameters
        self.confidence_threshold = 0.3
        self.fusion_window_size = 5  # frames to consider for fusion
        
        # Green color scheme
        self.bbox_color = (0, 255, 0)  # Green instead of blue
        self.text_color = (0, 255, 0)  # Green text
        self.bg_color = (0, 0, 0)      # Black background for text
        
    def process_multi_camera_video(self, video_paths: dict, output_path: str) -> dict:
        """
        Process multiple camera feeds with fusion and green styling
        """
        logger.info(f"🎯 Processing multi-camera fusion with green styling")
        logger.info(f"   Cameras: {list(video_paths.keys())}")
        logger.info(f"   Output: {output_path}")
        
        # Open all video captures
        caps = {}
        video_properties = {}
        
        for cam_id, video_path in video_paths.items():
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"Cannot open camera {cam_id}: {video_path}")
                continue
            
            caps[cam_id] = cap
            video_properties[cam_id] = {
                'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                'fps': cap.get(cv2.CAP_PROP_FPS),
                'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            }
            
            logger.info(f"   Camera {cam_id}: {video_properties[cam_id]['width']}x{video_properties[cam_id]['height']}, "
                       f"{video_properties[cam_id]['fps']} FPS, {video_properties[cam_id]['frame_count']} frames")
        
        if not caps:
            logger.error("No valid video captures available")
            return {"error": "No valid video captures"}
        
        # Setup output video (2x2 grid)
        grid_width = 1280 * 2  # 2560
        grid_height = 720 * 2  # 1440
        fps = 30
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (grid_width, grid_height))
        
        frame_num = 0
        total_detections = 0
        camera_results = {}
        
        # Initialize camera results
        for cam_id in caps.keys():
            camera_results[cam_id] = {
                'detections': 0,
                'tracks': 0,
                'avg_confidence': 0.0,
                'amino_grape_detections': 0
            }
        
        logger.info(f"🎬 Starting multi-camera processing...")
        
        while True:
            frames = {}
            all_tracks = {}
            
            # Read frames from all cameras
            for cam_id, cap in caps.items():
                ret, frame = cap.read()
                if not ret:
                    break
                frames[cam_id] = frame
                
                # Store current frame for tracker
                self.trackers[cam_id].current_frame = frame.copy()
                
                # Detect and track
                detections = self.trackers[cam_id].detect_hands_products(frame)
                tracks = self.trackers[cam_id].update_tracks(detections)
                
                all_tracks[cam_id] = tracks
                
                # Update statistics
                camera_results[cam_id]['detections'] += len(detections)
                if tracks:
                    camera_results[cam_id]['tracks'] += 1
                    for track in tracks:
                        if 'product_classification' in track:
                            classification = track['product_classification']
                            if classification.get('product_class') == 'amino_grape':
                                camera_results[cam_id]['amino_grape_detections'] += 1
            
            if not frames:
                break
            
            # Create 2x2 grid with green styling
            grid_frame = self.create_fusion_grid(frames, all_tracks)
            
            # Write frame
            out.write(grid_frame)
            
            frame_num += 1
            if frame_num % 50 == 0:
                logger.info(f"   Processed {frame_num} frames")
        
        # Cleanup
        for cap in caps.values():
            cap.release()
        out.release()
        
        # Calculate final statistics
        total_detections = sum(r['detections'] for r in camera_results.values())
        total_tracks = sum(r['tracks'] for r in camera_results.values())
        total_amino_grape = sum(r['amino_grape_detections'] for r in camera_results.values())
        
        result = {
            'output_path': output_path,
            'total_frames': frame_num,
            'total_detections': total_detections,
            'total_tracks': total_tracks,
            'total_amino_grape_detections': total_amino_grape,
            'camera_results': camera_results,
            'video_properties': video_properties
        }
        
        logger.info(f"✅ Multi-camera fusion completed:")
        logger.info(f"   Total frames: {frame_num}")
        logger.info(f"   Total detections: {total_detections}")
        logger.info(f"   Total tracks: {total_tracks}")
        logger.info(f"   Amino grape detections: {total_amino_grape}")
        logger.info(f"   Output saved to: {output_path}")
        
        return result
    
    def create_fusion_grid(self, frames: dict, all_tracks: dict) -> np.ndarray:
        """
        Create 2x2 grid with green bounding boxes and amino grape confidence only
        """
        # Create 2x2 grid
        grid_frame = np.zeros((1440, 2560, 3), dtype=np.uint8)
        
        # Camera positions in grid
        positions = {
            0: (0, 0),      # Top-left
            1: (1280, 0),   # Top-right
            2: (0, 720),    # Bottom-left
            3: (1280, 720)  # Bottom-right
        }
        
        for cam_id in range(4):
            if cam_id in frames:
                frame = frames[cam_id].copy()
                tracks = all_tracks.get(cam_id, [])
                
                # Draw green bounding boxes with amino grape confidence only
                for track in tracks:
                    bbox = track['bbox']
                    confidence = track['confidence']
                    track_id = track['track_id']
                    
                    # Draw GREEN bounding box
                    cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), 
                                self.bbox_color, 3)  # Thicker green line
                    
                    # Only show amino grape classification confidence
                    amino_confidence = None
                    if 'product_classification' in track:
                        classification = track['product_classification']
                        if classification.get('product_class') == 'amino_grape':
                            amino_confidence = classification.get('classification_confidence', 0.0)
                    
                    # Create label with only amino grape confidence
                    if amino_confidence is not None:
                        label = f"Amino Grape: {amino_confidence:.3f}"
                        
                        # Add black background for text
                        (text_width, text_height), _ = cv2.getTextSize(
                            label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
                        
                        cv2.rectangle(frame, 
                                    (bbox[0], bbox[1] - text_height - 10),
                                    (bbox[0] + text_width + 10, bbox[1]),
                                    self.bg_color, -1)
                        
                        # Draw green text
                        cv2.putText(frame, label, (bbox[0] + 5, bbox[1] - 5), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.text_color, 2)
                    else:
                        # If not amino grape, show minimal info
                        label = f"Product"
                        cv2.putText(frame, label, (bbox[0], bbox[1] - 10), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.text_color, 2)
                
                # Add camera label
                cam_label = f"Camera {cam_id}"
                cv2.putText(frame, cam_label, (10, 30), 
                          cv2.FONT_HERSHEY_SIMPLEX, 1.0, self.text_color, 2)
                
                # Place frame in grid
                x, y = positions[cam_id]
                grid_frame[y:y+720, x:x+1280] = frame
            else:
                # Empty camera slot
                x, y = positions[cam_id]
                cv2.putText(grid_frame, f"Camera {cam_id} - No Signal", 
                          (x + 400, y + 360), cv2.FONT_HERSHEY_SIMPLEX, 1.0, 
                          (0, 0, 255), 2)
        
        return grid_frame

def test_multi_camera_fusion_green():
    """Test the multi-camera fusion with green styling and extremely strict filtering"""
    
    print("🔧 TESTING MULTI-CAMERA FUSION WITH GREEN STYLING")
    print("=" * 70)
    print("🎯 Features:")
    print("   1. Extremely strict filtering (no teleportation)")
    print("   2. Green bounding boxes instead of blue")
    print("   3. Only amino grape confidence scores shown")
    print("   4. No size tripling or face jumping")
    print()
    
    # Define video paths for all 4 cameras
    video_paths = {
        0: "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4",
        1: "QuadCamTestVideos/AminoEnergyGrape/cam1.mp4", 
        2: "QuadCamTestVideos/AminoEnergyGrape/cam2.mp4",
        3: "QuadCamTestVideos/AminoEnergyGrape/cam3.mp4"
    }
    
    # Verify all videos exist
    for cam_id, path in video_paths.items():
        if not Path(path).exists():
            print(f"❌ Video not found: {path}")
            return None
    
    print("📹 All camera videos found, starting fusion processing...")
    
    # Create fusion system
    fusion_system = MultiCameraFusionGreen(
        hand_model_path="yolow-l_product_and_hand_detector.onnx",
        yolo_model_path="yolow-l_product_and_hand_detector.onnx"
    )
    
    output_path = "improved_tracking_results/multi_camera_fusion_green_strict.mp4"
    
    start_time = time.time()
    result = fusion_system.process_multi_camera_video(video_paths, output_path)
    processing_time = time.time() - start_time
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return
    
    print(f"✅ Multi-camera fusion completed in {processing_time:.1f}s")
    print(f"\n📊 RESULTS:")
    print("-" * 30)
    print(f"   Total frames: {result['total_frames']}")
    print(f"   Total detections: {result['total_detections']}")
    print(f"   Total tracks: {result['total_tracks']}")
    print(f"   Amino grape detections: {result['total_amino_grape_detections']}")
    print(f"   Output: {result['output_path']}")
    
    print(f"\n📹 PER-CAMERA RESULTS:")
    print("-" * 25)
    for cam_id, cam_result in result['camera_results'].items():
        print(f"   Camera {cam_id}: {cam_result['detections']} detections, "
              f"{cam_result['tracks']} tracks, "
              f"{cam_result['amino_grape_detections']} amino grape")
    
    # Save detailed results
    detailed_results = {
        'processing_time': processing_time,
        'result': result,
        'extremely_strict_filtering': [
            "Upper 60% frame filtering (no faces)",
            "Maximum 30% frame size (no full body)",
            "15% edge threshold (no edge artifacts)",
            "0.5x diagonal movement limit (no teleportation)",
            "0.7x to 1.4x size changes (no tripling)",
            "5% maximum size change per frame",
            "95% size smoothing factor",
            "Lower 60% frame positioning only",
            "Center 60% horizontal positioning only"
        ],
        'green_styling_features': [
            "Green bounding boxes instead of blue",
            "Only amino grape confidence scores displayed",
            "Clean, professional appearance",
            "Multi-camera 2x2 grid layout"
        ]
    }
    
    results_file = "improved_tracking_results/multi_camera_fusion_green_results.json"
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    return detailed_results

if __name__ == "__main__":
    results = test_multi_camera_fusion_green()
    
    print("\n" + "=" * 70)
    print("🎉 MULTI-CAMERA FUSION WITH GREEN STYLING COMPLETE")
    print("=" * 70)
    print("🔧 Extremely strict filtering implemented:")
    print("   ✅ No teleportation to faces (upper 60% filtered)")
    print("   ✅ No size tripling (max 40% size change)")
    print("   ✅ No full body coverage (max 30% frame size)")
    print("   ✅ No edge artifacts (15% edge threshold)")
    print("   ✅ Extremely conservative movement (0.5x diagonal)")
    print("\n🎨 Green styling features:")
    print("   ✅ Green bounding boxes instead of blue")
    print("   ✅ Only amino grape confidence scores shown")
    print("   ✅ Professional multi-camera layout")
    print("   ✅ Clean, focused tracking display")
    print("\n🎬 Review the multi-camera fusion video!")
    print("   - No more teleportation or size jumping")
    print("   - Green styling with amino grape confidence only")
    print("   - Professional 4-camera grid layout")
