#!/usr/bin/env python3
"""
Test the AI-based motion detection tracking system
"""

from onnx_product_hand_tracking_system import ONNXProductHandTracker
from pathlib import Path
import json

def test_ai_motion_tracking():
    """Test ONNX tracking with AI motion detection"""
    
    tracker = ONNXProductHandTracker()
    
    # Test on single video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    output_path = "onnx_tracking_results/test_ai_motion_tracking.mp4"
    
    print(f"Testing AI motion-based tracking on: {video_path}")
    print(f"Output will be saved to: {output_path}")
    print(f"Key improvements:")
    print(f"  ✅ AI motion detection (no hardcoded size filtering)")
    print(f"  ✅ Single bounding box per frame")
    print(f"  ✅ No ghost trails or duplicates")
    print(f"  ✅ Filters out stationary objects (products in fridge)")
    print(f"  ✅ Only tracks moving products in hand")
    
    # Process video
    result = tracker.process_video(video_path, output_path)
    
    # Save results
    results_file = "onnx_tracking_results/test_ai_motion_results.json"
    with open(results_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\nAI Motion Tracking Results:")
    print(f"  Total detections: {result['total_detections']}")
    print(f"  Unique tracks: {result['unique_tracks']}")
    print(f"  Average confidence: {result['avg_confidence']:.3f}")
    print(f"  Results saved to: {results_file}")
    
    # Compare with previous results
    try:
        with open("onnx_tracking_results/test_filtered_results.json", 'r') as f:
            old_results = json.load(f)
        
        print(f"\nComparison with previous (hardcoded filtering) results:")
        print(f"  Previous detections: {old_results['total_detections']}")
        print(f"  AI motion detections: {result['total_detections']}")
        print(f"  Previous tracks: {old_results['unique_tracks']}")
        print(f"  AI motion tracks: {result['unique_tracks']}")
        
        detection_change = result['total_detections'] - old_results['total_detections']
        track_change = result['unique_tracks'] - old_results['unique_tracks']
        
        print(f"  Detection change: {detection_change:+d}")
        print(f"  Track change: {track_change:+d}")
        
        if result['unique_tracks'] <= 5:
            print(f"  ✅ SUCCESS: Significantly reduced tracks (cleaner tracking)")
        if result['total_detections'] < old_results['total_detections']:
            print(f"  ✅ SUCCESS: Reduced false detections")
        
    except FileNotFoundError:
        print("No previous results found for comparison")
    
    print(f"\n🎯 Expected improvements:")
    print(f"  • No face/person bounding boxes")
    print(f"  • Single bounding box per frame")
    print(f"  • No ghost trails when product moves")
    print(f"  • Stationary fridge products ignored")
    print(f"  • Only moving products tracked")
    
    return result

if __name__ == "__main__":
    test_ai_motion_tracking()
