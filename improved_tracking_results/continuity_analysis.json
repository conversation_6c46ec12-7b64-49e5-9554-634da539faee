{"continuity_analysis": {"video_path": "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4", "total_frames": 438, "frames_with_tracks": 430, "tracking_coverage_percent": 98.17351598173516, "max_consecutive_gap": 8, "total_tracking_gaps": 1, "significant_gaps": 0, "total_size_jumps": 48, "significant_size_jumps": 43, "tracking_gaps_detail": [{"start_frame": 25, "end_frame": 32, "gap_length": 8}], "size_jumps_detail": [{"frame": 60, "size_ratio": 0.004455953358408053, "prev_area": 918322, "curr_area": 4092}, {"frame": 71, "size_ratio": 3.513333333333333, "prev_area": 4800, "curr_area": 16864}, {"frame": 75, "size_ratio": 0.6327087286527514, "prev_area": 16864, "curr_area": 10670}, {"frame": 78, "size_ratio": 8.108108108108109, "prev_area": 9768, "curr_area": 79200}, {"frame": 84, "size_ratio": 0.6692283061159142, "prev_area": 62460, "curr_area": 41800}], "frame_sample": [{"frame": 0, "detections": 1, "tracks": 1, "has_active_track": true, "track_id": 0, "confidence": 0.22799181938171387, "bbox": [675, 365, 728, 429], "bbox_area": 3392, "frames_since_update": 0, "validation_status": "new_track", "movement_distance": 0}, {"frame": 50, "detections": 0, "tracks": 1, "has_active_track": true, "track_id": 1, "confidence": 0.1, "bbox": [0, 0, 1279, 718], "bbox_area": 918322, "frames_since_update": 15, "validation_status": "valid_transition_smoothed", "movement_distance": 0.0, "size_ratio": 1.0}, {"frame": 100, "detections": 1, "tracks": 1, "has_active_track": true, "track_id": 5, "confidence": 0.504348635673523, "bbox": [548, 560, 684, 718], "bbox_area": 21488, "frames_since_update": 0, "validation_status": "valid_transition_smoothed", "movement_distance": 13.601470508735444, "size_ratio": 0.9551920341394026}, {"frame": 150, "detections": 0, "tracks": 1, "has_active_track": true, "track_id": 9, "confidence": 0.10906327086926894, "bbox": [0, 0, 1280, 719], "bbox_area": 920320, "frames_since_update": 10, "validation_status": "valid_transition_smoothed", "movement_distance": 0.0, "size_ratio": 1.0}, {"frame": 200, "detections": 1, "tracks": 1, "has_active_track": true, "track_id": 15, "confidence": 0.2960073947906494, "bbox": [512, 469, 909, 719], "bbox_area": 99250, "frames_since_update": 0, "validation_status": "valid_transition_smoothed", "movement_distance": 10.111874208078342, "size_ratio": 1.0121457489878543}, {"frame": 250, "detections": 1, "tracks": 1, "has_active_track": true, "track_id": 27, "confidence": 0.3311389684677124, "bbox": [825, 605, 978, 718], "bbox_area": 17289, "frames_since_update": 0, "validation_status": "valid_transition_smoothed", "movement_distance": 6.726812023536855, "size_ratio": 1.0977142857142856}, {"frame": 300, "detections": 0, "tracks": 1, "has_active_track": true, "track_id": 29, "confidence": 0.38916355073451997, "bbox": [792, 567, 943, 721], "bbox_area": 23254, "frames_since_update": 1, "validation_status": "valid_transition_smoothed", "movement_distance": 2.23606797749979, "size_ratio": 1.0}, {"frame": 350, "detections": 0, "tracks": 1, "has_active_track": true, "track_id": 36, "confidence": 0.20080415785312652, "bbox": [699, 387, 773, 442], "bbox_area": 4070, "frames_since_update": 1, "validation_status": "valid_transition_smoothed", "movement_distance": 13.341664064126334, "size_ratio": 1.0}, {"frame": 400, "detections": 1, "tracks": 1, "has_active_track": true, "track_id": 46, "confidence": 0.35248035192489624, "bbox": [761, 466, 856, 531], "bbox_area": 6175, "frames_since_update": 0, "validation_status": "valid_transition_smoothed", "movement_distance": 35.00357124637428, "size_ratio": 1.2705761316872428}]}, "video_processing_result": {"video_path": "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4", "output_path": "improved_tracking_results/continuity_fixed_cam0.mp4", "total_frames": 438, "total_detections": 262, "unique_tracks": 51, "avg_confidence": 0.3323536585381403, "detection_rate": 0.5981735159817352, "method": "improved_hand_product_tracking"}, "processing_time": 429.7036633491516, "fixes_implemented": ["Increased track timeout: 15 → 25 frames", "More sensitive motion detection: 300 → 200 threshold", "Size smoothing with 30% max change per frame", "Position prediction for tracking continuity", "Relaxed validation for better track matching", "Minimum confidence threshold to prevent track loss"], "expected_improvements": ["Reduced tracking gaps and freezing", "Smoother size transitions without jumping", "Better tracking continuity during fast movement", "More robust track matching and validation"]}