{"video_path": "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4", "output_path": "improved_tracking_results/aggressive_filtered_cam0.mp4", "processing_time": 438.1929178237915, "result": {"video_path": "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4", "output_path": "improved_tracking_results/aggressive_filtered_cam0.mp4", "total_frames": 438, "total_detections": 256, "unique_tracks": 66, "avg_confidence": 0.3412717177154612, "detection_rate": 0.5844748858447488, "method": "improved_hand_product_tracking"}, "aggressive_filters_implemented": ["FACE DETECTION FILTERING:", "- Upper 50% frame detection (expanded from 40%)", "- Square aspect ratio detection (0.5 to 2.0)", "- Face size detection (0.5% to 30% of frame)", "", "FULL FRAME FILTERING:", "- Width/height >80% of frame", "- Area >60% of frame", "- Oversized detection filtering", "", "EDGE DETECTION FILTERING:", "- Within 5% of frame edges", "- Corner and border detection", "", "SIZE AND POSITION VALIDATION:", "- Minimum 20px product size", "- Maximum 60% of frame size", "- Products in lower 80% of frame", "- Aspect ratio 0.2 to 5.0", "", "MOVEMENT CONSTRAINTS:", "- Maximum 1.0x diagonal movement (reduced from 1.5x)", "- Size change 0.5x to 2.0x (reduced from 0.3x to 3.0x)", "- Aspect ratio change <50% (reduced from 100%)"], "expected_results": ["Zero teleportation to faces", "Zero teleportation to full frame outline", "Zero edge detections", "Only valid product-in-hand tracking", "Smooth, continuous tracking without jumping"]}