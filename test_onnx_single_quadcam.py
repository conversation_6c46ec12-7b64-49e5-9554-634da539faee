#!/usr/bin/env python3
"""
Test ONNX tracking on a single QuadCam video
"""

from onnx_product_hand_tracking_system import ONNXProductHandTracker
from pathlib import Path
import json

def test_single_quadcam():
    """Test ONNX tracking on cam0 only"""
    
    tracker = ONNXProductHandTracker()
    
    # Test on single video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    output_path = "onnx_tracking_results/test_cam0_onnx_tracking.mp4"
    
    print(f"Testing ONNX tracking on: {video_path}")
    print(f"Output will be saved to: {output_path}")
    
    # Process video
    result = tracker.process_video(video_path, output_path)
    
    # Save results
    results_file = "onnx_tracking_results/test_cam0_results.json"
    with open(results_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\nResults:")
    print(f"  Total detections: {result['total_detections']}")
    print(f"  Unique tracks: {result['unique_tracks']}")
    print(f"  Average confidence: {result['avg_confidence']:.3f}")
    print(f"  Results saved to: {results_file}")
    
    return result

if __name__ == "__main__":
    test_single_quadcam()
