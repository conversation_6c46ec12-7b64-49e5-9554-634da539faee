#!/usr/bin/env python3
"""
Run improved single-object tracking on QuadCam videos
Fixes the issues with multiple false positive detections
"""

import sys
from pathlib import Path
import json
from datetime import datetime

# Import our custom system
from custom_amino_yolo_bytetrack_system import AminoYOLOByteTrackSystem

def main():
    print("\n" + "=" * 60)
    print("RUNNING IMPROVED SINGLE-OBJECT TRACKING")
    print("=" * 60)
    
    # Initialize system
    system = AminoYOLOByteTrackSystem()
    
    try:
        # Use existing trained model
        model_path = Path("custom_amino_bytetrack_models/amino_grape_yolon/weights/best.pt")
        if not model_path.exists():
            raise FileNotFoundError(f"Trained model not found at {model_path}")
        
        video_dir = Path("QuadCamTestVideos/AminoEnergyGrape")
        results_dir = Path("custom_amino_bytetrack_results_improved")
        results_dir.mkdir(exist_ok=True)
        
        print(f"Using model: {model_path}")
        print(f"Processing videos from: {video_dir}")
        print(f"Saving results to: {results_dir}")
        
        # Process all camera videos with improved tracking
        all_results = {}
        for cam_video in sorted(video_dir.glob("cam*.mp4")):
            cam_name = cam_video.stem
            output_video = results_dir / f"{cam_name}_amino_grape_improved.mp4"
            
            print(f"\nProcessing {cam_name} with improved single-object tracking...")
            print(f"  Input: {cam_video}")
            print(f"  Output: {output_video}")
            
            result = system.detect_and_track_video(str(model_path), str(cam_video), str(output_video))
            result["video_path"] = str(cam_video)
            result["output_path"] = str(output_video)
            result["status"] = "success"
            all_results[cam_name] = result
            
            # Print immediate results
            detections = result.get('detections', 0)
            tracks = result.get('tracks', 0)
            avg_conf = result.get('avg_confidence', 0)
            print(f"  Results: {detections} detections, {tracks} tracks, avg confidence: {avg_conf:.3f}")
        
        # Save comprehensive results
        results_file = results_dir / "improved_tracking_results.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        
        # Create complete pipeline results
        pipeline_results = {
            "pipeline_status": "completed_improved",
            "timestamp": str(datetime.now()),
            "model_path": str(model_path),
            "results_directory": str(results_dir),
            "processed_videos": len(all_results),
            "total_detections": sum(r["detections"] for r in all_results.values()),
            "total_tracks": sum(r["tracks"] for r in all_results.values()),
            "improvements": [
                "Higher confidence threshold (0.6 vs 0.3)",
                "Movement-based detection filtering",
                "Single object tracking constraint",
                "Size-based detection filtering (0.1% - 30% of frame)",
                "Combined confidence + movement scoring",
                "Lost track recovery with timeout"
            ],
            "camera_results": all_results
        }
        
        pipeline_file = results_dir / "improved_pipeline_results.json"
        with open(pipeline_file, 'w') as f:
            json.dump(pipeline_results, f, indent=2)
        
        print("\n" + "=" * 60)
        print("IMPROVED TRACKING PIPELINE COMPLETED!")
        print("=" * 60)
        print(f"Results saved to: {results_dir}")
        print(f"Model used: {model_path}")
        print(f"Total videos processed: {len(all_results)}")
        print(f"Total detections: {pipeline_results['total_detections']}")
        print(f"Total tracks: {pipeline_results['total_tracks']}")
        
        # Print per-camera summary
        print("\nPer-camera results (improved tracking):")
        for cam_name, result in all_results.items():
            detections = result.get('detections', 0)
            tracks = result.get('tracks', 0)
            avg_conf = result.get('avg_confidence', 0)
            print(f"  {cam_name}: {detections} detections, {tracks} tracks, avg conf: {avg_conf:.3f}")
            
        print("\nKey improvements implemented:")
        for improvement in pipeline_results['improvements']:
            print(f"  • {improvement}")
            
        print(f"\nCheck the output videos in: {results_dir}")
        print("The videos should now show single bounding box tracking the product in hand!")
            
    except Exception as e:
        print(f"Improved tracking pipeline failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
