#!/usr/bin/env python3
"""
Test the tracking continuity fixes for freezing and size jumping issues
"""

from improved_hand_product_tracker import ImprovedHandProductTracker
import json
import time
import cv2
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_tracking_continuity(video_path: str) -> dict:
    """
    Analyze tracking continuity by examining frame-by-frame tracking data
    """
    
    tracker = ImprovedHandProductTracker(
        hand_model_path="yolow-l_product_and_hand_detector.onnx",
        yolo_model_path="yolow-l_product_and_hand_detector.onnx"
    )
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return {"error": f"Cannot open video: {video_path}"}
    
    # Reset tracking state
    tracker.active_tracks = {}
    tracker.next_track_id = 0
    tracker.frame_count = 0
    tracker.background_subtractor = cv2.createBackgroundSubtractorMOG2(
        detectShadows=False, varThreshold=16, history=500)
    
    frame_data = []
    tracking_gaps = []
    size_jumps = []
    prev_track_data = None
    
    frame_num = 0
    consecutive_no_detection = 0
    max_gap = 0
    
    print(f"🔍 Analyzing tracking continuity for: {video_path}")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Store current frame for classification
        tracker.current_frame = frame.copy()
        
        # Detect hands and products
        detections = tracker.detect_hands_products(frame)
        
        # Update tracks
        tracks = tracker.update_tracks(detections)
        
        # Analyze this frame
        frame_info = {
            'frame': frame_num,
            'detections': len(detections),
            'tracks': len(tracks),
            'has_active_track': len(tracks) > 0
        }
        
        if tracks:
            track = tracks[0]  # First track
            bbox = track['bbox']
            
            frame_info.update({
                'track_id': track['track_id'],
                'confidence': track['confidence'],
                'bbox': bbox,
                'bbox_area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                'frames_since_update': track.get('frames_since_update', 0),
                'validation_status': track.get('validation_status', 'unknown'),
                'movement_distance': track.get('movement_distance', 0)
            })
            
            # Check for size jumps
            if prev_track_data and prev_track_data['has_active_track']:
                prev_area = prev_track_data['bbox_area']
                curr_area = frame_info['bbox_area']
                size_ratio = curr_area / prev_area if prev_area > 0 else 1.0
                
                frame_info['size_ratio'] = size_ratio
                
                # Detect significant size jumps (>50% change in one frame)
                if size_ratio > 1.5 or size_ratio < 0.67:
                    size_jumps.append({
                        'frame': frame_num,
                        'size_ratio': size_ratio,
                        'prev_area': prev_area,
                        'curr_area': curr_area
                    })
            
            consecutive_no_detection = 0
        else:
            consecutive_no_detection += 1
            frame_info['consecutive_no_detection'] = consecutive_no_detection
            
            # Track gaps
            if consecutive_no_detection > 5:  # Gap of more than 5 frames
                if not tracking_gaps or tracking_gaps[-1]['end_frame'] != frame_num - 1:
                    tracking_gaps.append({
                        'start_frame': frame_num - consecutive_no_detection + 1,
                        'end_frame': frame_num,
                        'gap_length': consecutive_no_detection
                    })
                else:
                    tracking_gaps[-1]['end_frame'] = frame_num
                    tracking_gaps[-1]['gap_length'] = consecutive_no_detection
        
        max_gap = max(max_gap, consecutive_no_detection)
        frame_data.append(frame_info)
        prev_track_data = frame_info
        
        frame_num += 1
        
        if frame_num % 100 == 0:
            print(f"   Analyzed {frame_num} frames...")
    
    cap.release()
    
    # Calculate statistics
    total_frames = len(frame_data)
    frames_with_tracks = len([f for f in frame_data if f['has_active_track']])
    tracking_coverage = frames_with_tracks / total_frames * 100 if total_frames > 0 else 0
    
    # Analyze gaps
    significant_gaps = [g for g in tracking_gaps if g['gap_length'] > 10]
    
    # Analyze size jumps
    significant_size_jumps = [s for s in size_jumps if s['size_ratio'] > 2.0 or s['size_ratio'] < 0.5]
    
    analysis = {
        'video_path': video_path,
        'total_frames': total_frames,
        'frames_with_tracks': frames_with_tracks,
        'tracking_coverage_percent': tracking_coverage,
        'max_consecutive_gap': max_gap,
        'total_tracking_gaps': len(tracking_gaps),
        'significant_gaps': len(significant_gaps),
        'total_size_jumps': len(size_jumps),
        'significant_size_jumps': len(significant_size_jumps),
        'tracking_gaps_detail': tracking_gaps[:5],  # First 5 gaps
        'size_jumps_detail': size_jumps[:5],  # First 5 size jumps
        'frame_sample': frame_data[::50]  # Every 50th frame
    }
    
    return analysis

def test_tracking_continuity_fixes():
    """Test the fixes for tracking continuity issues"""
    
    print("🔧 TESTING TRACKING CONTINUITY FIXES")
    print("=" * 70)
    print("🎯 Testing fixes for:")
    print("   1. Bounding box freezing/lagging")
    print("   2. Size jumping in single frames")
    print()
    
    # Test video
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    
    # Analyze tracking continuity
    print("📊 Analyzing tracking continuity...")
    analysis = analyze_tracking_continuity(video_path)
    
    if "error" in analysis:
        print(f"❌ Error: {analysis['error']}")
        return
    
    print(f"\n📈 TRACKING CONTINUITY ANALYSIS RESULTS:")
    print("-" * 50)
    print(f"📊 Total frames: {analysis['total_frames']}")
    print(f"📊 Frames with tracks: {analysis['frames_with_tracks']}")
    print(f"📊 Tracking coverage: {analysis['tracking_coverage_percent']:.1f}%")
    print(f"📊 Max consecutive gap: {analysis['max_consecutive_gap']} frames")
    print(f"📊 Total tracking gaps: {analysis['total_tracking_gaps']}")
    print(f"📊 Significant gaps (>10 frames): {analysis['significant_gaps']}")
    print(f"📊 Total size jumps: {analysis['total_size_jumps']}")
    print(f"📊 Significant size jumps (>2x or <0.5x): {analysis['significant_size_jumps']}")
    
    # Validation
    print(f"\n🎯 VALIDATION RESULTS:")
    print("-" * 30)
    
    # Check 1: Tracking coverage should be high
    if analysis['tracking_coverage_percent'] >= 70:
        print("✅ PASS: Good tracking coverage (≥70%)")
    else:
        print(f"❌ FAIL: Low tracking coverage ({analysis['tracking_coverage_percent']:.1f}%)")
    
    # Check 2: No long gaps (freezing)
    if analysis['max_consecutive_gap'] <= 15:
        print("✅ PASS: No long tracking gaps (≤15 frames)")
    else:
        print(f"❌ FAIL: Long tracking gap detected ({analysis['max_consecutive_gap']} frames)")
    
    # Check 3: Few significant size jumps
    if analysis['significant_size_jumps'] <= 5:
        print("✅ PASS: Few significant size jumps (≤5)")
    else:
        print(f"❌ FAIL: Too many size jumps ({analysis['significant_size_jumps']})")
    
    # Check 4: Overall tracking quality
    if analysis['significant_gaps'] <= 2:
        print("✅ PASS: Few significant tracking gaps (≤2)")
    else:
        print(f"❌ FAIL: Too many significant gaps ({analysis['significant_gaps']})")
    
    # Now test with video processing
    print(f"\n🎬 Creating improved tracking video...")
    
    tracker = ImprovedHandProductTracker(
        hand_model_path="yolow-l_product_and_hand_detector.onnx",
        yolo_model_path="yolow-l_product_and_hand_detector.onnx"
    )
    
    output_path = "improved_tracking_results/continuity_fixed_cam0.mp4"
    
    start_time = time.time()
    result = tracker.process_video(video_path, output_path)
    processing_time = time.time() - start_time
    
    print(f"✅ Video processing completed in {processing_time:.1f}s")
    print(f"   Detections: {result['total_detections']}")
    print(f"   Tracks: {result['unique_tracks']}")
    print(f"   Avg Confidence: {result['avg_confidence']:.3f}")
    print(f"   Output: {output_path}")
    
    # Save detailed analysis
    detailed_results = {
        'continuity_analysis': analysis,
        'video_processing_result': result,
        'processing_time': processing_time,
        'fixes_implemented': [
            "Increased track timeout: 15 → 25 frames",
            "More sensitive motion detection: 300 → 200 threshold",
            "Size smoothing with 30% max change per frame",
            "Position prediction for tracking continuity",
            "Relaxed validation for better track matching",
            "Minimum confidence threshold to prevent track loss"
        ],
        'expected_improvements': [
            "Reduced tracking gaps and freezing",
            "Smoother size transitions without jumping",
            "Better tracking continuity during fast movement",
            "More robust track matching and validation"
        ]
    }
    
    results_file = "improved_tracking_results/continuity_analysis.json"
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n💾 Detailed analysis saved to: {results_file}")
    
    return detailed_results

if __name__ == "__main__":
    results = test_tracking_continuity_fixes()
    
    print("\n" + "=" * 70)
    print("🎉 TRACKING CONTINUITY TESTING COMPLETE")
    print("=" * 70)
    print("🔧 Fixes implemented:")
    print("   ✅ Increased track timeout to prevent freezing")
    print("   ✅ Size smoothing to prevent jumping")
    print("   ✅ Position prediction for better continuity")
    print("   ✅ More sensitive motion detection")
    print("   ✅ Relaxed validation for better tracking")
    print("\n🎬 Review the output video to see the improvements!")
    print("   - Continuous tracking without freezing")
    print("   - Smooth size transitions without jumping")
    print("   - Better tracking during fast movement")
